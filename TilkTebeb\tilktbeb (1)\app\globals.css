@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* New color palette based on warm beige to earthy brown */
    --background: 39 54% 87%; /* Light beige #F5E8C7 */
    --foreground: 10 20% 18%; /* Dark brown #3A1F1D for text */

    --card: 39 54% 95%; /* Lighter beige for cards */
    --card-foreground: 10 20% 18%;

    --popover: 39 54% 95%;
    --popover-foreground: 10 20% 18%;

    --primary: 36 58% 44%; /* Amber/brown #B1732E */
    --primary-foreground: 39 54% 97%;

    --secondary: 43 77% 52%; /* Muted gold #D4AF37 */
    --secondary-foreground: 10 20% 18%;

    --muted: 39 30% 88%; /* Muted beige */
    --muted-foreground: 10 20% 40%;

    --accent: 36 80% 82%; /* Highlight color */
    --accent-foreground: 10 20% 18%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 39 54% 97%;

    --border: 39 30% 82%; /* Border color */
    --input: 39 30% 82%;
    --ring: 36 58% 44%;

    --radius: 1rem; /* Rounded corners */
  }

  .dark {
    --background: 10 20% 18%; /* Dark brown #3A1F1D */
    --foreground: 39 54% 97%; /* Light beige for text */

    --card: 10 25% 25%; /* Darker brown for cards */
    --card-foreground: 39 54% 97%;

    --popover: 10 25% 25%;
    --popover-foreground: 39 54% 97%;

    --primary: 36 58% 44%; /* Amber/brown #B1732E */
    --primary-foreground: 39 54% 97%;

    --secondary: 43 77% 52%; /* Muted gold #D4AF37 */
    --secondary-foreground: 10 20% 18%;

    --muted: 10 20% 30%; /* Darker muted brown */
    --muted-foreground: 39 30% 80%;

    --accent: 36 80% 82%; /* Highlight color */
    --accent-foreground: 10 20% 18%;

    --destructive: 0 62% 30%;
    --destructive-foreground: 39 54% 97%;

    --border: 10 20% 35%; /* Dark border */
    --input: 10 20% 35%;
    --ring: 36 58% 44%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    font-family: "Inter", sans-serif;
    background: linear-gradient(to bottom, #f5e8c7, #e8d5b5, #d4b996, #b69b7d, #8c715a, #6b4f3f, #4a2c2a);
    background-attachment: fixed;
    background-size: 100% 100%;
    @apply text-foreground;
  }

  /* Typography enhancements */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-bold text-foreground;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  }

  /* Animated background particles */
  .particles-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    opacity: 0.15;
    pointer-events: none;
  }

  /* Glassmorphism styles */
  .glass {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  }

  /* 3D Card effects */
  .card-3d {
    transition: all 0.3s ease;
    transform: perspective(1000px) rotateX(0) rotateY(0);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 6px 6px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  .card-3d:hover {
    transform: perspective(1000px) rotateX(2deg) rotateY(2deg) scale(1.03);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15), 0 10px 10px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2);
  }

  /* Button hover effects */
  .btn-hover {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .btn-hover:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
    transition: 0.6s;
  }

  .btn-hover:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 7px 14px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.1);
  }

  .btn-hover:hover:before {
    transform: translateX(100%);
  }

  /* Glow effect for demo button */
  .btn-glow:hover {
    box-shadow: 0 0 15px rgba(212, 175, 55, 0.5);
  }

  /* Section dividers */
  .section-divider {
    position: relative;
    height: 100px;
    margin-top: -50px;
    margin-bottom: -50px;
  }

  .section-divider svg {
    position: absolute;
    width: 100%;
    height: 100px;
    fill: rgba(255, 245, 225, 0.8);
    filter: drop-shadow(0 5px 5px rgba(0, 0, 0, 0.1));
  }

  /* Book card hover effects */
  .book-card {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .book-card:hover {
    transform: translateY(-10px) rotateX(5deg) rotateY(5deg) scale(1.05);
    box-shadow: 0 20px 30px rgba(0, 0, 0, 0.2), 0 0 20px rgba(212, 175, 55, 0.2);
  }

  .book-card img {
    transition: transform 0.5s ease;
  }

  .book-card:hover img {
    transform: scale(1.08);
  }

  /* Custom scrollbar for better user experience */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(177, 115, 46, 0.7);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(177, 115, 46, 0.9);
  }

  /* Gold accents */
  .gold-accent {
    color: #d4af37;
  }

  .gold-icon {
    color: #d4af37;
    filter: drop-shadow(0 0 2px rgba(212, 175, 55, 0.3));
  }

  /* Dark mode adjustments */
  .dark .glass {
    background: rgba(0, 0, 0, 0.3);
  }

  .dark .section-divider svg {
    fill: rgba(58, 31, 29, 0.8);
  }

  .dark body {
    background: linear-gradient(to bottom, #3a1f1d, #2d1915, #20100e);
    background-attachment: fixed;
    background-size: 100% 100%;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(177, 115, 46, 0.5);
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(177, 115, 46, 0.7);
  }

  /* 3D Flashcard flip effect */
  .perspective-1000 {
    perspective: 1000px;
  }

  .preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }
}

