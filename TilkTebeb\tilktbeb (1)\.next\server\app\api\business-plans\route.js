/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/business-plans/route";
exports.ids = ["app/api/business-plans/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbusiness-plans%2Froute&page=%2Fapi%2Fbusiness-plans%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbusiness-plans%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5CDesktop%5CProjects%5CAstewai%5CTilkTebeb%5Ctilktbeb%20(1)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5CDesktop%5CProjects%5CAstewai%5CTilkTebeb%5Ctilktbeb%20(1)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbusiness-plans%2Froute&page=%2Fapi%2Fbusiness-plans%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbusiness-plans%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5CDesktop%5CProjects%5CAstewai%5CTilkTebeb%5Ctilktbeb%20(1)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5CDesktop%5CProjects%5CAstewai%5CTilkTebeb%5Ctilktbeb%20(1)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_pc_Desktop_Projects_Astewai_TilkTebeb_tilktbeb_1_app_api_business_plans_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/business-plans/route.ts */ \"(rsc)/./app/api/business-plans/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/business-plans/route\",\n        pathname: \"/api/business-plans\",\n        filename: \"route\",\n        bundlePath: \"app/api/business-plans/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\api\\\\business-plans\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_pc_Desktop_Projects_Astewai_TilkTebeb_tilktbeb_1_app_api_business_plans_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbusiness-plans%2Froute&page=%2Fapi%2Fbusiness-plans%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbusiness-plans%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5CDesktop%5CProjects%5CAstewai%5CTilkTebeb%5Ctilktbeb%20(1)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5CDesktop%5CProjects%5CAstewai%5CTilkTebeb%5Ctilktbeb%20(1)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/api/business-plans/route.ts":
/*!*****************************************!*\
  !*** ./app/api/business-plans/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Sample database of business plans\nconst businessPlansDB = [\n    {\n        id: \"small-1\",\n        title: \"Local Coffee Shop\",\n        category: \"Food & Beverage\",\n        size: \"small\",\n        description: \"A comprehensive business plan for starting and operating a successful local coffee shop.\",\n        overview: `\n      <h3>Executive Summary</h3>\n      <p>This business plan outlines the establishment of a cozy, community-focused coffee shop that serves premium coffee and light food items. The shop will be located in a high-traffic area with significant foot traffic and limited direct competition.</p>\n      \n      <h3>Business Concept</h3>\n      <ul>\n        <li>Premium coffee and espresso drinks</li>\n        <li>Fresh pastries and light meals</li>\n        <li>Comfortable seating and free Wi-Fi</li>\n        <li>Focus on sustainable practices</li>\n      </ul>\n      \n      <h3>Target Market</h3>\n      <ul>\n        <li>Young professionals (25-40)</li>\n        <li>College students</li>\n        <li>Remote workers</li>\n        <li>Local residents</li>\n      </ul>\n    `,\n        marketAnalysis: `\n      <h3>Market Overview</h3>\n      <p>The coffee shop industry continues to grow, with increasing demand for premium coffee experiences. Key market trends include:</p>\n      <ul>\n        <li>Growing preference for specialty coffee</li>\n        <li>Increased focus on sustainability</li>\n        <li>Rising demand for plant-based options</li>\n        <li>Need for comfortable workspaces</li>\n      </ul>\n      \n      <h3>Competitive Analysis</h3>\n      <p>Local competition includes:</p>\n      <ul>\n        <li>Chain coffee shops (2 within 1km)</li>\n        <li>Independent cafes (1 within 1km)</li>\n        <li>Restaurants serving coffee</li>\n      </ul>\n      \n      <h3>Competitive Advantage</h3>\n      <ul>\n        <li>Premium quality coffee</li>\n        <li>Comfortable atmosphere</li>\n        <li>Excellent customer service</li>\n        <li>Strategic location</li>\n      </ul>\n    `,\n        financials: `\n      <h3>Startup Costs</h3>\n      <ul>\n        <li>Lease deposit and improvements: $25,000</li>\n        <li>Equipment: $35,000</li>\n        <li>Initial inventory: $5,000</li>\n        <li>Licenses and permits: $2,000</li>\n        <li>Working capital: $20,000</li>\n      </ul>\n      \n      <h3>Financial Projections</h3>\n      <p>Year 1:</p>\n      <ul>\n        <li>Revenue: $300,000</li>\n        <li>Expenses: $270,000</li>\n        <li>Net profit: $30,000</li>\n      </ul>\n      \n      <p>Year 2:</p>\n      <ul>\n        <li>Revenue: $400,000</li>\n        <li>Expenses: $340,000</li>\n        <li>Net profit: $60,000</li>\n      </ul>\n    `,\n        implementation: `\n      <h3>Timeline</h3>\n      <ul>\n        <li>Month 1-2: Location selection and lease signing</li>\n        <li>Month 2-3: Design and permits</li>\n        <li>Month 3-4: Construction and equipment installation</li>\n        <li>Month 4: Staff hiring and training</li>\n        <li>Month 5: Soft opening and marketing</li>\n        <li>Month 6: Grand opening</li>\n      </ul>\n      \n      <h3>Marketing Strategy</h3>\n      <ul>\n        <li>Social media presence</li>\n        <li>Local partnerships</li>\n        <li>Loyalty program</li>\n        <li>Community events</li>\n      </ul>\n      \n      <h3>Risk Mitigation</h3>\n      <ul>\n        <li>Comprehensive insurance coverage</li>\n        <li>Diverse supplier relationships</li>\n        <li>Staff training programs</li>\n        <li>Cash flow management</li>\n      </ul>\n    `,\n        isPremium: false\n    },\n    {\n        id: \"small-2\",\n        title: \"Freelance Web Development\",\n        category: \"Technology\",\n        size: \"small\",\n        description: \"A detailed business plan for starting and growing a freelance web development business.\",\n        overview: `\n      <h3>Executive Summary</h3>\n      <p>This business plan outlines the establishment of a freelance web development business focused on creating custom websites and web applications for small to medium-sized businesses. The business will operate remotely with minimal overhead costs.</p>\n      \n      <h3>Business Concept</h3>\n      <ul>\n        <li>Custom website development</li>\n        <li>Web application development</li>\n        <li>Website maintenance and support</li>\n        <li>SEO and digital marketing services</li>\n      </ul>\n      \n      <h3>Target Market</h3>\n      <ul>\n        <li>Small businesses needing online presence</li>\n        <li>Medium-sized companies requiring web applications</li>\n        <li>Startups with limited budgets</li>\n        <li>Non-profit organizations</li>\n      </ul>\n    `,\n        marketAnalysis: `\n      <h3>Market Overview</h3>\n      <p>The web development industry continues to grow as businesses increasingly recognize the importance of online presence. Key market trends include:</p>\n      <ul>\n        <li>Increasing demand for mobile-responsive websites</li>\n        <li>Growing need for e-commerce functionality</li>\n        <li>Rising importance of user experience (UX) design</li>\n        <li>Shift toward progressive web applications</li>\n      </ul>\n      \n      <h3>Competitive Analysis</h3>\n      <p>Competition includes:</p>\n      <ul>\n        <li>Other freelance developers</li>\n        <li>Web development agencies</li>\n        <li>DIY website builders (Wix, Squarespace)</li>\n      </ul>\n      \n      <h3>Competitive Advantage</h3>\n      <ul>\n        <li>Personalized service and direct client communication</li>\n        <li>Lower overhead costs than agencies</li>\n        <li>Specialized expertise in modern frameworks</li>\n        <li>Flexible pricing models</li>\n      </ul>\n    `,\n        financials: `\n      <h3>Startup Costs</h3>\n      <ul>\n        <li>Computer equipment: $3,000</li>\n        <li>Software subscriptions: $1,200/year</li>\n        <li>Website and hosting: $500</li>\n        <li>Business registration: $300</li>\n        <li>Initial marketing: $1,000</li>\n      </ul>\n      \n      <h3>Financial Projections</h3>\n      <p>Year 1:</p>\n      <ul>\n        <li>Revenue: $60,000</li>\n        <li>Expenses: $15,000</li>\n        <li>Net profit: $45,000</li>\n      </ul>\n      \n      <p>Year 2:</p>\n      <ul>\n        <li>Revenue: $90,000</li>\n        <li>Expenses: $20,000</li>\n        <li>Net profit: $70,000</li>\n      </ul>\n    `,\n        implementation: `\n      <h3>Timeline</h3>\n      <ul>\n        <li>Month 1: Business registration and website setup</li>\n        <li>Month 2: Portfolio development</li>\n        <li>Month 3: Initial marketing and networking</li>\n        <li>Month 4-6: Secure first clients and build reputation</li>\n      </ul>\n      \n      <h3>Marketing Strategy</h3>\n      <ul>\n        <li>Portfolio website showcasing work</li>\n        <li>Social media presence on LinkedIn and Twitter</li>\n        <li>Content marketing through blog posts</li>\n        <li>Networking at local business events</li>\n        <li>Referral program for existing clients</li>\n      </ul>\n      \n      <h3>Risk Mitigation</h3>\n      <ul>\n        <li>Diversify client base to avoid dependency</li>\n        <li>Maintain emergency fund for slow periods</li>\n        <li>Continuous skill development</li>\n        <li>Clear contracts and scope definitions</li>\n      </ul>\n    `,\n        isPremium: false\n    },\n    {\n        id: \"small-3\",\n        title: \"Mobile Food Truck\",\n        category: \"Food & Beverage\",\n        size: \"small\",\n        description: \"A comprehensive business plan for launching and operating a successful mobile food truck business.\",\n        overview: `\n      <h3>Executive Summary</h3>\n      <p>This business plan outlines the establishment of a mobile food truck business serving specialty cuisine with a focus on fresh, locally-sourced ingredients. The food truck will operate in high-traffic urban areas, business districts, and special events.</p>\n      \n      <h3>Business Concept</h3>\n      <ul>\n        <li>Specialty cuisine with unique menu offerings</li>\n        <li>Focus on fresh, locally-sourced ingredients</li>\n        <li>Mobile operation with multiple locations</li>\n        <li>Social media-driven marketing and location updates</li>\n      </ul>\n      \n      <h3>Target Market</h3>\n      <ul>\n        <li>Urban professionals (25-45)</li>\n        <li>Lunch crowds in business districts</li>\n        <li>Event attendees (festivals, concerts, etc.)</li>\n        <li>Food enthusiasts seeking unique dining experiences</li>\n      </ul>\n    `,\n        marketAnalysis: `\n      <h3>Market Overview</h3>\n      <p>The food truck industry has experienced significant growth in recent years. Key market trends include:</p>\n      <ul>\n        <li>Increasing consumer interest in diverse, ethnic cuisines</li>\n        <li>Growing preference for quick, affordable dining options</li>\n        <li>Rising demand for sustainable and locally-sourced food</li>\n        <li>Popularity of unique dining experiences</li>\n      </ul>\n      \n      <h3>Competitive Analysis</h3>\n      <p>Competition includes:</p>\n      <ul>\n        <li>Other food trucks (varying cuisines)</li>\n        <li>Quick-service restaurants</li>\n        <li>Fast-casual dining establishments</li>\n        <li>Cafes and delis</li>\n      </ul>\n      \n      <h3>Competitive Advantage</h3>\n      <ul>\n        <li>Unique menu offerings not available elsewhere</li>\n        <li>Mobility to follow customer demand</li>\n        <li>Lower overhead costs than traditional restaurants</li>\n        <li>Strong social media presence and following</li>\n      </ul>\n    `,\n        financials: `\n      <h3>Startup Costs</h3>\n      <ul>\n        <li>Food truck purchase/renovation: $50,000</li>\n        <li>Kitchen equipment: $15,000</li>\n        <li>Initial inventory: $3,000</li>\n        <li>Permits and licenses: $2,000</li>\n        <li>Insurance: $2,500</li>\n        <li>Marketing and branding: $3,000</li>\n      </ul>\n      \n      <h3>Financial Projections</h3>\n      <p>Year 1:</p>\n      <ul>\n        <li>Revenue: $180,000</li>\n        <li>Expenses: $150,000</li>\n        <li>Net profit: $30,000</li>\n      </ul>\n      \n      <p>Year 2:</p>\n      <ul>\n        <li>Revenue: $250,000</li>\n        <li>Expenses: $190,000</li>\n        <li>Net profit: $60,000</li>\n      </ul>\n    `,\n        implementation: `\n      <h3>Timeline</h3>\n      <ul>\n        <li>Month 1-2: Food truck acquisition and renovation</li>\n        <li>Month 2-3: Menu development and testing</li>\n        <li>Month 3: Permits and licenses acquisition</li>\n        <li>Month 4: Staff hiring and training</li>\n        <li>Month 5: Soft launch and marketing</li>\n        <li>Month 6: Full operation</li>\n      </ul>\n      \n      <h3>Marketing Strategy</h3>\n      <ul>\n        <li>Strong social media presence with location updates</li>\n        <li>Distinctive branding and truck design</li>\n        <li>Participation in food truck festivals and events</li>\n        <li>Loyalty program for repeat customers</li>\n        <li>Partnerships with local businesses and offices</li>\n      </ul>\n      \n      <h3>Risk Mitigation</h3>\n      <ul>\n        <li>Weather contingency plans</li>\n        <li>Diversified location strategy</li>\n        <li>Comprehensive insurance coverage</li>\n        <li>Flexible menu to adapt to ingredient availability and costs</li>\n        <li>Maintenance fund for vehicle repairs</li>\n      </ul>\n    `,\n        isPremium: true\n    },\n    {\n        id: \"medium-1\",\n        title: \"Boutique Digital Agency\",\n        category: \"Marketing\",\n        size: \"medium\",\n        description: \"A comprehensive business plan for establishing and growing a boutique digital marketing agency.\",\n        overview: `\n      <h3>Executive Summary</h3>\n      <p>This business plan outlines the establishment of a boutique digital agency specializing in comprehensive digital marketing services for small to medium-sized businesses. The agency will focus on delivering personalized, results-driven strategies across multiple digital channels.</p>\n      \n      <h3>Business Concept</h3>\n      <ul>\n        <li>Full-service digital marketing solutions</li>\n        <li>Data-driven strategy development</li>\n        <li>Creative content production</li>\n        <li>Performance-based pricing options</li>\n      </ul>\n      \n      <h3>Target Market</h3>\n      <ul>\n        <li>Small to medium-sized businesses</li>\n        <li>E-commerce companies</li>\n        <li>Professional service providers</li>\n        <li>Local businesses seeking growth</li>\n      </ul>\n    `,\n        marketAnalysis: `\n      <h3>Market Overview</h3>\n      <p>The digital marketing industry continues to grow as businesses increasingly shift advertising budgets to digital channels. Key market trends include:</p>\n      <ul>\n        <li>Increasing focus on ROI and measurable results</li>\n        <li>Growing importance of content marketing</li>\n        <li>Rising demand for video and interactive content</li>\n        <li>Shift toward personalized marketing experiences</li>\n      </ul>\n      \n      <h3>Competitive Analysis</h3>\n      <p>Competition includes:</p>\n      <ul>\n        <li>Large full-service agencies</li>\n        <li>Specialized boutique agencies</li>\n        <li>Freelancers and consultants</li>\n        <li>In-house marketing departments</li>\n      </ul>\n      \n      <h3>Competitive Advantage</h3>\n      <ul>\n        <li>Personalized service with dedicated account managers</li>\n        <li>Flexible engagement models</li>\n        <li>Specialized expertise in emerging platforms</li>\n        <li>Data-driven approach with transparent reporting</li>\n      </ul>\n    `,\n        financials: `\n      <h3>Startup Costs</h3>\n      <ul>\n        <li>Office space and setup: $30,000</li>\n        <li>Technology and software: $20,000</li>\n        <li>Initial staffing: $150,000</li>\n        <li>Legal and administrative: $5,000</li>\n        <li>Marketing and branding: $15,000</li>\n        <li>Working capital: $80,000</li>\n      </ul>\n      \n      <h3>Financial Projections</h3>\n      <p>Year 1:</p>\n      <ul>\n        <li>Revenue: $500,000</li>\n        <li>Expenses: $450,000</li>\n        <li>Net profit: $50,000</li>\n      </ul>\n      \n      <p>Year 2:</p>\n      <ul>\n        <li>Revenue: $900,000</li>\n        <li>Expenses: $750,000</li>\n        <li>Net profit: $150,000</li>\n      </ul>\n      \n      <p>Year 3:</p>\n      <ul>\n        <li>Revenue: $1,500,000</li>\n        <li>Expenses: $1,200,000</li>\n        <li>Net profit: $300,000</li>\n      </ul>\n    `,\n        implementation: `\n      <h3>Timeline</h3>\n      <ul>\n        <li>Month 1-2: Business setup and registration</li>\n        <li>Month 2-3: Office setup and initial hiring</li>\n        <li>Month 3-4: Service development and pricing strategy</li>\n        <li>Month 4-5: Marketing and initial client acquisition</li>\n        <li>Month 6: Full operation with initial clients</li>\n      </ul>\n      \n      <h3>Marketing Strategy</h3>\n      <ul>\n        <li>Showcase website with case studies and results</li>\n        <li>Content marketing demonstrating expertise</li>\n        <li>Strategic partnerships with complementary service providers</li>\n        <li>Networking at industry events</li>\n        <li>Referral program for existing clients</li>\n      </ul>\n      \n      <h3>Organizational Structure</h3>\n      <ul>\n        <li>Founder/CEO: Strategic direction and business development</li>\n        <li>Creative Director: Oversees content and design</li>\n        <li>Digital Marketing Strategist: Campaign planning and execution</li>\n        <li>Account Managers: Client relationship management</li>\n        <li>Specialists: SEO, PPC, Social Media, Content Creation</li>\n      </ul>\n    `,\n        isPremium: true\n    },\n    {\n        id: \"large-1\",\n        title: \"Enterprise SaaS Platform\",\n        category: \"Technology\",\n        size: \"large\",\n        description: \"A comprehensive business plan for developing and scaling an enterprise SaaS platform.\",\n        overview: `\n      <h3>Executive Summary</h3>\n      <p>This business plan outlines the development and scaling of an enterprise Software-as-a-Service (SaaS) platform designed to solve specific industry challenges. The platform will be built on a subscription model with tiered pricing based on features and user counts.</p>\n      \n      <h3>Business Concept</h3>\n      <ul>\n        <li>Cloud-based enterprise software solution</li>\n        <li>Subscription-based revenue model</li>\n        <li>Modular architecture for customization</li>\n        <li>API-first approach for integration</li>\n      </ul>\n      \n      <h3>Target Market</h3>\n      <ul>\n        <li>Mid-market enterprises (100-1000 employees)</li>\n        <li>Large corporations (1000+ employees)</li>\n        <li>Specific industry verticals with unique challenges</li>\n        <li>Organizations undergoing digital transformation</li>\n      </ul>\n    `,\n        marketAnalysis: `\n      <h3>Market Overview</h3>\n      <p>The enterprise SaaS market continues to grow as organizations increasingly adopt cloud-based solutions. Key market trends include:</p>\n      <ul>\n        <li>Shift from on-premise to cloud-based solutions</li>\n        <li>Growing demand for integrated platforms</li>\n        <li>Increasing focus on data security and compliance</li>\n        <li>Rising importance of AI and automation</li>\n      </ul>\n      \n      <h3>Competitive Analysis</h3>\n      <p>Competition includes:</p>\n      <ul>\n        <li>Established enterprise software vendors</li>\n        <li>Specialized SaaS providers</li>\n        <li>Legacy systems with cloud offerings</li>\n        <li>In-house developed solutions</li>\n      </ul>\n      \n      <h3>Competitive Advantage</h3>\n      <ul>\n        <li>Modern, user-friendly interface</li>\n        <li>Flexible integration capabilities</li>\n        <li>Industry-specific features and workflows</li>\n        <li>Advanced analytics and reporting</li>\n        <li>Responsive customer support and implementation services</li>\n      </ul>\n    `,\n        financials: `\n      <h3>Startup Costs</h3>\n      <ul>\n        <li>Product development: $1,500,000</li>\n        <li>Infrastructure and hosting: $200,000</li>\n        <li>Initial team: $1,000,000</li>\n        <li>Sales and marketing: $500,000</li>\n        <li>Legal and compliance: $150,000</li>\n        <li>Office and operations: $250,000</li>\n        <li>Working capital: $1,400,000</li>\n      </ul>\n      \n      <h3>Financial Projections</h3>\n      <p>Year 1:</p>\n      <ul>\n        <li>Revenue: $1,000,000</li>\n        <li>Expenses: $3,000,000</li>\n        <li>Net loss: $2,000,000</li>\n      </ul>\n      \n      <p>Year 2:</p>\n      <ul>\n        <li>Revenue: $3,500,000</li>\n        <li>Expenses: $4,500,000</li>\n        <li>Net loss: $1,000,000</li>\n      </ul>\n      \n      <p>Year 3:</p>\n      <ul>\n        <li>Revenue: $8,000,000</li>\n        <li>Expenses: $6,000,000</li>\n        <li>Net profit: $2,000,000</li>\n      </ul>\n      \n      <p>Year 5:</p>\n      <ul>\n        <li>Revenue: $25,000,000</li>\n        <li>Expenses: $15,000,000</li>\n        <li>Net profit: $10,000,000</li>\n      </ul>\n    `,\n        implementation: `\n      <h3>Development Timeline</h3>\n      <ul>\n        <li>Months 1-6: MVP development</li>\n        <li>Months 7-9: Beta testing with select customers</li>\n        <li>Month 10: Initial public release</li>\n        <li>Months 11-18: Feature expansion and platform maturation</li>\n        <li>Months 19-24: Enterprise feature set and scaling</li>\n      </ul>\n      \n      <h3>Go-to-Market Strategy</h3>\n      <ul>\n        <li>Initial focus on specific industry vertical</li>\n        <li>Content marketing establishing thought leadership</li>\n        <li>Direct sales team for enterprise clients</li>\n        <li>Strategic partnerships with consultancies and integrators</li>\n        <li>Industry conferences and events</li>\n      </ul>\n      \n      <h3>Organizational Structure</h3>\n      <ul>\n        <li>Executive team: CEO, CTO, CFO, CMO</li>\n        <li>Product development: Engineering, Product Management, Design</li>\n        <li>Customer success: Implementation, Support, Training</li>\n        <li>Sales and marketing: Direct Sales, Marketing, Partnerships</li>\n        <li>Operations: Finance, HR, Legal, IT</li>\n      </ul>\n      \n      <h3>Funding Strategy</h3>\n      <ul>\n        <li>Seed round: $2M for MVP development</li>\n        <li>Series A: $5M for initial market entry</li>\n        <li>Series B: $15M for scaling and expansion</li>\n        <li>Potential Series C or strategic acquisition in year 4-5</li>\n      </ul>\n    `,\n        isPremium: true\n    }\n];\n// GET handler for retrieving all business plans or filtered plans\nasync function GET(request) {\n    const { searchParams } = new URL(request.url);\n    // Extract query parameters\n    const size = searchParams.get(\"size\");\n    const category = searchParams.get(\"category\");\n    const query = searchParams.get(\"query\");\n    const premium = searchParams.get(\"premium\");\n    // Filter business plans based on query parameters\n    let filteredPlans = [\n        ...businessPlansDB\n    ];\n    if (size && size !== \"all\") {\n        filteredPlans = filteredPlans.filter((plan)=>plan.size === size);\n    }\n    if (category && category !== \"all\") {\n        filteredPlans = filteredPlans.filter((plan)=>plan.category.toLowerCase() === category.toLowerCase());\n    }\n    if (query) {\n        const searchQuery = query.toLowerCase();\n        filteredPlans = filteredPlans.filter((plan)=>plan.title.toLowerCase().includes(searchQuery) || plan.category.toLowerCase().includes(searchQuery));\n    }\n    if (premium !== null) {\n        const isPremium = premium === \"true\";\n        filteredPlans = filteredPlans.filter((plan)=>plan.isPremium === isPremium);\n    }\n    // Return only the necessary information for listing\n    const planPreviews = filteredPlans.map((plan)=>({\n            id: plan.id,\n            title: plan.title,\n            category: plan.category,\n            size: plan.size,\n            description: plan.description,\n            isPremium: plan.isPremium\n        }));\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(planPreviews);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/business-plans/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbusiness-plans%2Froute&page=%2Fapi%2Fbusiness-plans%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbusiness-plans%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5CDesktop%5CProjects%5CAstewai%5CTilkTebeb%5Ctilktbeb%20(1)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5CDesktop%5CProjects%5CAstewai%5CTilkTebeb%5Ctilktbeb%20(1)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();