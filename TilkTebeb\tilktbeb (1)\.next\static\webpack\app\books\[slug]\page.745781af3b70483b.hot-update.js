"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/books/[slug]/page",{

/***/ "(app-pages-browser)/./app/books/[slug]/page.tsx":
/*!***********************************!*\
  !*** ./app/books/[slug]/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BookPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _components_book_bookmark__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/book-bookmark */ \"(app-pages-browser)/./components/book-bookmark.tsx\");\n/* harmony import */ var _components_offline_book_toggle__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/offline-book-toggle */ \"(app-pages-browser)/./components/offline-book-toggle.tsx\");\n/* harmony import */ var _lib_offline_storage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/offline-storage */ \"(app-pages-browser)/./lib/offline-storage.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _components_reading_progress_bar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/reading-progress-bar */ \"(app-pages-browser)/./components/reading-progress-bar.tsx\");\n/* harmony import */ var _components_highlight_controls__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/highlight-controls */ \"(app-pages-browser)/./components/highlight-controls.tsx\");\n/* harmony import */ var _components_notes_panel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/notes-panel */ \"(app-pages-browser)/./components/notes-panel.tsx\");\n/* harmony import */ var _components_text_to_speech_player__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/text-to-speech-player */ \"(app-pages-browser)/./components/text-to-speech-player.tsx\");\n/* harmony import */ var _components_flashcard_creator__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/flashcard-creator */ \"(app-pages-browser)/./components/flashcard-creator.tsx\");\n/* harmony import */ var _components_reading_speed_tracker__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/reading-speed-tracker */ \"(app-pages-browser)/./components/reading-speed-tracker.tsx\");\n/* harmony import */ var _components_social_sharing__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/social-sharing */ \"(app-pages-browser)/./components/social-sharing.tsx\");\n/* harmony import */ var _components_comprehension_quiz__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/comprehension-quiz */ \"(app-pages-browser)/./components/comprehension-quiz.tsx\");\n/* harmony import */ var _components_recommendations_similar_content__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/recommendations/similar-content */ \"(app-pages-browser)/./components/recommendations/similar-content.tsx\");\n/* harmony import */ var _lib_analytics__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/lib/analytics */ \"(app-pages-browser)/./lib/analytics.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction BookPage(param) {\n    let { params } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [book, setBook] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [fontSize, setFontSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"medium\");\n    const [isOffline, setIsOffline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOfflineAvailable, setIsOfflineAvailable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [readingProgress, setReadingProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showNotes, setShowNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isHighlighting, setIsHighlighting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"reading\");\n    const [selectedTextForSharing, setSelectedTextForSharing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isTtsPlaying, setIsTtsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [readingStartTime, setReadingStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [readingSessionId, setReadingSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Check online status\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BookPage.useEffect\": ()=>{\n            setIsOffline(!navigator.onLine);\n            const handleOnline = {\n                \"BookPage.useEffect.handleOnline\": ()=>setIsOffline(false)\n            }[\"BookPage.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"BookPage.useEffect.handleOffline\": ()=>setIsOffline(true)\n            }[\"BookPage.useEffect.handleOffline\"];\n            window.addEventListener(\"online\", handleOnline);\n            window.addEventListener(\"offline\", handleOffline);\n            return ({\n                \"BookPage.useEffect\": ()=>{\n                    window.removeEventListener(\"online\", handleOnline);\n                    window.removeEventListener(\"offline\", handleOffline);\n                }\n            })[\"BookPage.useEffect\"];\n        }\n    }[\"BookPage.useEffect\"], []);\n    // Fetch book data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BookPage.useEffect\": ()=>{\n            const fetchBook = {\n                \"BookPage.useEffect.fetchBook\": async ()=>{\n                    try {\n                        var _bookData_categories;\n                        setIsLoading(true);\n                        // First check if book is available offline\n                        const offlineAvailable = await (0,_lib_offline_storage__WEBPACK_IMPORTED_MODULE_9__.isBookAvailableOffline)(params.slug);\n                        setIsOfflineAvailable(offlineAvailable);\n                        if (isOffline && !offlineAvailable) {\n                            // If offline and book not available offline\n                            setError(\"You're offline and this book isn't available offline. Please connect to the internet or choose a book you've saved for offline reading.\");\n                            setIsLoading(false);\n                            return;\n                        }\n                        let bookData = null;\n                        if (offlineAvailable) {\n                            // Get from offline storage\n                            bookData = await (0,_lib_offline_storage__WEBPACK_IMPORTED_MODULE_9__.getOfflineBook)(params.slug);\n                        } else {\n                            // Fetch from API\n                            bookData = await _lib_api__WEBPACK_IMPORTED_MODULE_21__.api.getBookById(params.slug);\n                        }\n                        setBook(bookData);\n                        // Load reading progress\n                        const progress = await (0,_lib_offline_storage__WEBPACK_IMPORTED_MODULE_9__.getReadingProgress)(params.slug);\n                        if (progress) {\n                            setReadingProgress(progress);\n                        }\n                        // Track view activity\n                        // In a real app, we would get the user ID from authentication\n                        const mockUserId = \"user-123\";\n                        (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"view\", bookData.id, \"book\", {\n                            title: bookData.title,\n                            category: ((_bookData_categories = bookData.categories) === null || _bookData_categories === void 0 ? void 0 : _bookData_categories[0]) || \"Uncategorized\"\n                        });\n                        // Start reading session\n                        setReadingStartTime(new Date());\n                        setReadingSessionId(\"reading-session-\".concat(Date.now()));\n                    } catch (err) {\n                        console.error(\"Error fetching book:\", err);\n                        setError(\"Error loading book details. Please try again later.\");\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"BookPage.useEffect.fetchBook\"];\n            fetchBook();\n            // Cleanup function to track reading time when component unmounts\n            return ({\n                \"BookPage.useEffect\": ()=>{\n                    if (book && readingStartTime) {\n                        const endTime = new Date();\n                        const timeSpentMinutes = Math.round((endTime.getTime() - readingStartTime.getTime()) / 60000);\n                        if (timeSpentMinutes > 0) {\n                            var _book_categories;\n                            // Track read activity with time spent\n                            const mockUserId = \"user-123\";\n                            (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"read\", book.id, \"book\", {\n                                title: book.title,\n                                category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\",\n                                timeSpent: timeSpentMinutes,\n                                progress: (readingProgress === null || readingProgress === void 0 ? void 0 : readingProgress.completionPercentage) || 0,\n                                sessionId: readingSessionId\n                            });\n                        }\n                    }\n                }\n            })[\"BookPage.useEffect\"];\n        }\n    }[\"BookPage.useEffect\"], [\n        params.slug,\n        isOffline\n    ]);\n    // Save reading progress periodically\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BookPage.useEffect\": ()=>{\n            if (!book || !contentRef.current) return;\n            const content = contentRef.current;\n            const totalLength = content.scrollHeight;\n            const saveProgress = {\n                \"BookPage.useEffect.saveProgress\": async ()=>{\n                    const position = content.scrollTop;\n                    const completionPercentage = Math.min(100, Math.round(position / (totalLength - content.clientHeight) * 100));\n                    const progress = {\n                        position,\n                        totalLength,\n                        lastReadAt: new Date().toISOString(),\n                        completionPercentage: isNaN(completionPercentage) ? 0 : completionPercentage\n                    };\n                    setReadingProgress(progress);\n                    try {\n                        await (0,_lib_offline_storage__WEBPACK_IMPORTED_MODULE_9__.saveReadingProgress)(book.id, progress);\n                        // Track progress update if significant change (every 10%)\n                        if (progress.completionPercentage % 10 === 0) {\n                            var _book_categories;\n                            const mockUserId = \"user-123\";\n                            (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"read\", book.id, \"book\", {\n                                title: book.title,\n                                category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\",\n                                progress: progress.completionPercentage,\n                                sessionId: readingSessionId\n                            });\n                            // If completed, track completion\n                            if (progress.completionPercentage >= 90) {\n                                var _book_categories1;\n                                (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"complete\", book.id, \"book\", {\n                                    title: book.title,\n                                    category: ((_book_categories1 = book.categories) === null || _book_categories1 === void 0 ? void 0 : _book_categories1[0]) || \"Uncategorized\"\n                                });\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Error saving reading progress:\", error);\n                    }\n                }\n            }[\"BookPage.useEffect.saveProgress\"];\n            // Save progress on scroll (debounced)\n            let timeout;\n            const handleScroll = {\n                \"BookPage.useEffect.handleScroll\": ()=>{\n                    clearTimeout(timeout);\n                    timeout = setTimeout(saveProgress, 500);\n                }\n            }[\"BookPage.useEffect.handleScroll\"];\n            content.addEventListener(\"scroll\", handleScroll);\n            // Restore scroll position from saved progress\n            if (readingProgress === null || readingProgress === void 0 ? void 0 : readingProgress.position) {\n                content.scrollTop = readingProgress.position;\n            }\n            // Save progress when leaving the page\n            const handleBeforeUnload = {\n                \"BookPage.useEffect.handleBeforeUnload\": ()=>{\n                    saveProgress();\n                }\n            }[\"BookPage.useEffect.handleBeforeUnload\"];\n            window.addEventListener(\"beforeunload\", handleBeforeUnload);\n            return ({\n                \"BookPage.useEffect\": ()=>{\n                    content.removeEventListener(\"scroll\", handleScroll);\n                    window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n                    clearTimeout(timeout);\n                    saveProgress();\n                }\n            })[\"BookPage.useEffect\"];\n        }\n    }[\"BookPage.useEffect\"], [\n        book,\n        readingProgress === null || readingProgress === void 0 ? void 0 : readingProgress.position,\n        readingSessionId\n    ]);\n    // Handle text selection for sharing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BookPage.useEffect\": ()=>{\n            const handleSelection = {\n                \"BookPage.useEffect.handleSelection\": ()=>{\n                    const selection = window.getSelection();\n                    if (selection && !selection.isCollapsed) {\n                        setSelectedTextForSharing(selection.toString());\n                    }\n                }\n            }[\"BookPage.useEffect.handleSelection\"];\n            document.addEventListener(\"mouseup\", handleSelection);\n            return ({\n                \"BookPage.useEffect\": ()=>{\n                    document.removeEventListener(\"mouseup\", handleSelection);\n                }\n            })[\"BookPage.useEffect\"];\n        }\n    }[\"BookPage.useEffect\"], []);\n    const fontSizeClasses = {\n        small: \"text-sm leading-6\",\n        medium: \"text-base leading-7\",\n        large: \"text-lg leading-8\"\n    };\n    const handleOfflineStatusChange = (available)=>{\n        setIsOfflineAvailable(available);\n        // Track download activity if made available offline\n        if (available && book) {\n            var _book_categories;\n            const mockUserId = \"user-123\";\n            (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"download\", book.id, \"book\", {\n                title: book.title,\n                category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\"\n            });\n        }\n    };\n    const handleTtsPlayingChange = (playing)=>{\n        setIsTtsPlaying(playing);\n    };\n    // Estimate word count for reading speed tracking\n    const getWordCount = ()=>{\n        if (!book) return 0;\n        // Strip HTML tags and count words\n        const text = book.summary.replace(/<[^>]*>/g, \"\");\n        return text.split(/\\s+/).length;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-8 md:py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/books\",\n                    className: \"inline-flex items-center gap-1 text-muted-foreground hover:text-foreground mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Back to Books\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center items-center h-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            className: \"h-8 w-8 animate-spin text-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"ml-2\",\n                            children: \"Loading book details...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n            lineNumber: 271,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !book) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-8 md:py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/books\",\n                    className: \"inline-flex items-center gap-1 text-muted-foreground hover:text-foreground mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Back to Books\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-destructive mb-4\",\n                            children: error || \"Book not found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>router.push(\"/books\"),\n                            children: \"Browse Books\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n            lineNumber: 287,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-8 md:py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/books\",\n                            className: \"inline-flex items-center gap-1 text-muted-foreground hover:text-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Back to Books\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: isOffline ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 px-2 py-1 rounded-full text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Offline Mode\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 px-2 py-1 rounded-full text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Online\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, this),\n                readingProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_reading_progress_bar__WEBPACK_IMPORTED_MODULE_11__.ReadingProgressBar, {\n                    progress: readingProgress.completionPercentage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 29\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sticky top-24 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: book.coverUrl || \"/placeholder.svg\",\n                                                alt: \"\".concat(book.title, \" book cover\"),\n                                                className: \"w-full max-w-[240px] mx-auto rounded-xl shadow-md card-themed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 space-y-4 gradient-text-overlay p-4 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: book.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: book.author\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-4 text-sm text-muted-foreground\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-primary font-semibold\",\n                                                                        children: book.rating\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        width: \"16\",\n                                                                        height: \"16\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"currentColor\",\n                                                                        stroke: \"none\",\n                                                                        className: \"text-secondary\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                            points: \"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 355,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        book.pages,\n                                                                        \" pages\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: book.language\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-4 flex flex-wrap gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_book_bookmark__WEBPACK_IMPORTED_MODULE_7__.BookBookmark, {\n                                                                bookId: book.id,\n                                                                bookTitle: book.title,\n                                                                onBookmark: ()=>{\n                                                                    var _book_categories;\n                                                                    // Track bookmark activity\n                                                                    const mockUserId = \"user-123\";\n                                                                    (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"bookmark\", book.id, \"book\", {\n                                                                        title: book.title,\n                                                                        category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\"\n                                                                    });\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_offline_book_toggle__WEBPACK_IMPORTED_MODULE_8__.OfflineBookToggle, {\n                                                                book: book,\n                                                                onStatusChange: handleOfflineStatusChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_social_sharing__WEBPACK_IMPORTED_MODULE_17__.SocialSharing, {\n                                                                contentId: book.id,\n                                                                contentTitle: book.title,\n                                                                contentType: \"book\",\n                                                                quote: selectedTextForSharing || undefined\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === \"reading\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_reading_speed_tracker__WEBPACK_IMPORTED_MODULE_16__.ReadingSpeedTracker, {\n                                        contentId: book.id,\n                                        contentLength: getWordCount()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 43\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_recommendations_similar_content__WEBPACK_IMPORTED_MODULE_19__.SimilarContent, {\n                                        contentId: book.id,\n                                        contentType: \"book\",\n                                        contentTitle: book.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                                defaultValue: \"reading\",\n                                value: activeTab,\n                                onValueChange: setActiveTab,\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"reading\",\n                                                className: \"gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Reading\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"tools\",\n                                                className: \"gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Learning Tools\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"quiz\",\n                                                className: \"gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Quiz\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"reading\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                            className: \"p-6 rounded-xl border-none shadow-sm card-themed\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: fontSize === \"small\" ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"rounded-full h-8 w-8 p-0\",\n                                                                    onClick: ()=>setFontSize(\"small\"),\n                                                                    children: \"S\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: fontSize === \"medium\" ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"rounded-full h-8 w-8 p-0\",\n                                                                    onClick: ()=>setFontSize(\"medium\"),\n                                                                    children: \"M\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: fontSize === \"large\" ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"rounded-full h-8 w-8 p-0\",\n                                                                    onClick: ()=>setFontSize(\"large\"),\n                                                                    children: \"L\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: isHighlighting ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"gap-1\",\n                                                                    onClick: ()=>setIsHighlighting(!isHighlighting),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"2\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"m9 11-6 6v3h9l3-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 461,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"m22 12-4.6 4.6a2 2 0 0 1-2.8 0l-5.2-5.2a2 2 0 0 1 0-2.8L14 4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 462,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 450,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Highlight\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 464,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: showNotes ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"gap-1\",\n                                                                    onClick: ()=>{\n                                                                        setShowNotes(!showNotes);\n                                                                        // Track note creation activity\n                                                                        if (!showNotes && book) {\n                                                                            var _book_categories;\n                                                                            const mockUserId = \"user-123\";\n                                                                            (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"note_create\", book.id, \"book\", {\n                                                                                title: book.title,\n                                                                                category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\"\n                                                                            });\n                                                                        }\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 484,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Notes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 \".concat(showNotes ? \"pr-4\" : \"\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                                                                defaultValue: \"summary\",\n                                                                className: \"w-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                                                        className: \"mb-4 bg-muted/50 p-1 rounded-full\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                                                value: \"summary\",\n                                                                                className: \"rounded-full\",\n                                                                                children: \"Summary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 494,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                                                value: \"key-insights\",\n                                                                                className: \"rounded-full\",\n                                                                                children: \"Key Insights\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 497,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                                                value: \"applications\",\n                                                                                className: \"rounded-full\",\n                                                                                children: \"Applications\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 500,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 493,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        ref: contentRef,\n                                                                        className: \"max-h-[70vh] overflow-y-auto pr-4 custom-scrollbar\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                                                                value: \"summary\",\n                                                                                className: \"\".concat(fontSizeClasses[fontSize], \" space-y-4\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    dangerouslySetInnerHTML: {\n                                                                                        __html: book.summary\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 507,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 506,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                                                                value: \"key-insights\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"\".concat(fontSizeClasses[fontSize], \" space-y-4\"),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        dangerouslySetInnerHTML: {\n                                                                                            __html: book.keyInsights || book.summary\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                        lineNumber: 512,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 511,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 510,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                                                                value: \"applications\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"\".concat(fontSizeClasses[fontSize], \" space-y-4\"),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        dangerouslySetInnerHTML: {\n                                                                                            __html: book.applications || \"\"\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                        lineNumber: 518,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 517,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 516,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 505,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showNotes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-64 border-l pl-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notes_panel__WEBPACK_IMPORTED_MODULE_13__.NotesPanel, {\n                                                                contentId: book.id\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isHighlighting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 border-t pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_highlight_controls__WEBPACK_IMPORTED_MODULE_12__.HighlightControls, {\n                                                        contentId: book.id\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 21\n                                                }, this),\n                                                !isTtsPlaying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 border-t pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_text_to_speech_player__WEBPACK_IMPORTED_MODULE_14__.TextToSpeechPlayer, {\n                                                        text: book.summary.replace(/<[^>]*>/g, \"\"),\n                                                        onPlayingChange: handleTtsPlayingChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"tools\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                    className: \"p-6 rounded-xl border-none shadow-sm card-themed\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-bold mb-4\",\n                                                            children: \"Flashcards\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flashcard_creator__WEBPACK_IMPORTED_MODULE_15__.FlashcardCreator, {\n                                                            contentId: book.id,\n                                                            contentTitle: book.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                    className: \"p-6 rounded-xl border-none shadow-sm card-themed\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-bold mb-4\",\n                                                            children: \"Text-to-Speech\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_text_to_speech_player__WEBPACK_IMPORTED_MODULE_14__.TextToSpeechPlayer, {\n                                                            text: book.summary.replace(/<[^>]*>/g, \"\"),\n                                                            onPlayingChange: handleTtsPlayingChange\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"quiz\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                            className: \"p-6 rounded-xl border-none shadow-sm card-themed\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold mb-4\",\n                                                    children: \"Test Your Understanding\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_comprehension_quiz__WEBPACK_IMPORTED_MODULE_18__.ComprehensionQuiz, {\n                                                    contentId: book.id,\n                                                    contentTitle: book.title,\n                                                    contentType: \"book\",\n                                                    onQuizAttempt: ()=>{\n                                                        var _book_categories;\n                                                        // Track quiz attempt activity\n                                                        const mockUserId = \"user-123\";\n                                                        (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"quiz_attempt\", book.id, \"book\", {\n                                                            title: book.title,\n                                                            category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\"\n                                                        });\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n            lineNumber: 305,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n        lineNumber: 304,\n        columnNumber: 5\n    }, this);\n}\n_s(BookPage, \"yR5tjC9fYAccchtyl4fak/7BNFU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = BookPage;\nvar _c;\n$RefreshReg$(_c, \"BookPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9ib29rcy9bc2x1Z10vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDdkI7QUFDZTtBQUNJO0FBQ0o7QUFDb0M7QUFDaUM7QUFDdkQ7QUFDVztBQUNtRDtBQUMzRTtBQUUwQjtBQUNIO0FBQ2Q7QUFDa0I7QUFDTjtBQUNPO0FBQ2I7QUFDUTtBQUNVO0FBQzFCO0FBQ0o7QUFFaEMsU0FBU3FDLFNBQVMsS0FBd0M7UUFBeEMsRUFBRUMsTUFBTSxFQUFnQyxHQUF4Qzs7SUFDL0IsTUFBTUMsU0FBU25DLDBEQUFTQTtJQUN4QixNQUFNLEVBQUVvQyxLQUFLLEVBQUUsR0FBR2YsMkRBQVFBO0lBQzFCLE1BQU1nQixhQUFhdkMsNkNBQU1BLENBQWlCO0lBQzFDLE1BQU0sQ0FBQ3dDLE1BQU1DLFFBQVEsR0FBRzNDLCtDQUFRQSxDQUFjO0lBQzlDLE1BQU0sQ0FBQzRDLFdBQVdDLGFBQWEsR0FBRzdDLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQzhDLE9BQU9DLFNBQVMsR0FBRy9DLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNnRCxVQUFVQyxZQUFZLEdBQUdqRCwrQ0FBUUEsQ0FBK0I7SUFDdkUsTUFBTSxDQUFDa0QsV0FBV0MsYUFBYSxHQUFHbkQsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDb0Qsb0JBQW9CQyxzQkFBc0IsR0FBR3JELCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQ3NELGlCQUFpQkMsbUJBQW1CLEdBQUd2RCwrQ0FBUUEsQ0FLNUM7SUFDVixNQUFNLENBQUN3RCxXQUFXQyxhQUFhLEdBQUd6RCwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUMwRCxnQkFBZ0JDLGtCQUFrQixHQUFHM0QsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDNEQsV0FBV0MsYUFBYSxHQUFHN0QsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDOEQsd0JBQXdCQywwQkFBMEIsR0FBRy9ELCtDQUFRQSxDQUFnQjtJQUNwRixNQUFNLENBQUNnRSxjQUFjQyxnQkFBZ0IsR0FBR2pFLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ2tFLGtCQUFrQkMsb0JBQW9CLEdBQUduRSwrQ0FBUUEsQ0FBYztJQUN0RSxNQUFNLENBQUNvRSxrQkFBa0JDLG9CQUFvQixHQUFHckUsK0NBQVFBLENBQWdCO0lBRXhFLHNCQUFzQjtJQUN0QkMsZ0RBQVNBOzhCQUFDO1lBQ1JrRCxhQUFhLENBQUNtQixVQUFVQyxNQUFNO1lBRTlCLE1BQU1DO21EQUFlLElBQU1yQixhQUFhOztZQUN4QyxNQUFNc0I7b0RBQWdCLElBQU10QixhQUFhOztZQUV6Q3VCLE9BQU9DLGdCQUFnQixDQUFDLFVBQVVIO1lBQ2xDRSxPQUFPQyxnQkFBZ0IsQ0FBQyxXQUFXRjtZQUVuQztzQ0FBTztvQkFDTEMsT0FBT0UsbUJBQW1CLENBQUMsVUFBVUo7b0JBQ3JDRSxPQUFPRSxtQkFBbUIsQ0FBQyxXQUFXSDtnQkFDeEM7O1FBQ0Y7NkJBQUcsRUFBRTtJQUVMLGtCQUFrQjtJQUNsQnhFLGdEQUFTQTs4QkFBQztZQUNSLE1BQU00RTtnREFBWTtvQkFDaEIsSUFBSTs0QkF1Q1VDO3dCQXRDWmpDLGFBQWE7d0JBRWIsMkNBQTJDO3dCQUMzQyxNQUFNa0MsbUJBQW1CLE1BQU12RCw0RUFBc0JBLENBQUNjLE9BQU8wQyxJQUFJO3dCQUNqRTNCLHNCQUFzQjBCO3dCQUV0QixJQUFJN0IsYUFBYSxDQUFDNkIsa0JBQWtCOzRCQUNsQyw0Q0FBNEM7NEJBQzVDaEMsU0FDRTs0QkFFRkYsYUFBYTs0QkFDYjt3QkFDRjt3QkFFQSxJQUFJaUMsV0FBd0I7d0JBRTVCLElBQUlDLGtCQUFrQjs0QkFDcEIsMkJBQTJCOzRCQUMzQkQsV0FBVyxNQUFNekQsb0VBQWNBLENBQUNpQixPQUFPMEMsSUFBSTt3QkFDN0MsT0FBTzs0QkFDTCxpQkFBaUI7NEJBQ2pCRixXQUFXLE1BQU0xQywwQ0FBR0EsQ0FBQzZDLFdBQVcsQ0FBQzNDLE9BQU8wQyxJQUFJO3dCQUM5Qzt3QkFFQXJDLFFBQVFtQzt3QkFFUix3QkFBd0I7d0JBQ3hCLE1BQU1JLFdBQVcsTUFBTTNELHdFQUFrQkEsQ0FBQ2UsT0FBTzBDLElBQUk7d0JBQ3JELElBQUlFLFVBQVU7NEJBQ1ozQixtQkFBbUIyQjt3QkFDckI7d0JBRUEsc0JBQXNCO3dCQUN0Qiw4REFBOEQ7d0JBQzlELE1BQU1DLGFBQWE7d0JBQ25CaEQsa0VBQWlCQSxDQUFDZ0QsWUFBWSxRQUFRTCxTQUFTTSxFQUFFLEVBQUUsUUFBUTs0QkFDekRDLE9BQU9QLFNBQVNPLEtBQUs7NEJBQ3JCQyxVQUFVUixFQUFBQSx1QkFBQUEsU0FBU1MsVUFBVSxjQUFuQlQsMkNBQUFBLG9CQUFxQixDQUFDLEVBQUUsS0FBSTt3QkFDeEM7d0JBRUEsd0JBQXdCO3dCQUN4Qlgsb0JBQW9CLElBQUlxQjt3QkFDeEJuQixvQkFBb0IsbUJBQThCLE9BQVhtQixLQUFLQyxHQUFHO29CQUNqRCxFQUFFLE9BQU9DLEtBQUs7d0JBQ1pDLFFBQVE3QyxLQUFLLENBQUMsd0JBQXdCNEM7d0JBQ3RDM0MsU0FBUztvQkFDWCxTQUFVO3dCQUNSRixhQUFhO29CQUNmO2dCQUNGOztZQUVBZ0M7WUFFQSxpRUFBaUU7WUFDakU7c0NBQU87b0JBQ0wsSUFBSW5DLFFBQVF3QixrQkFBa0I7d0JBQzVCLE1BQU0wQixVQUFVLElBQUlKO3dCQUNwQixNQUFNSyxtQkFBbUJDLEtBQUtDLEtBQUssQ0FBQyxDQUFDSCxRQUFRSSxPQUFPLEtBQUs5QixpQkFBaUI4QixPQUFPLEVBQUMsSUFBSzt3QkFFdkYsSUFBSUgsbUJBQW1CLEdBQUc7Z0NBS1puRDs0QkFKWixzQ0FBc0M7NEJBQ3RDLE1BQU15QyxhQUFhOzRCQUNuQmhELGtFQUFpQkEsQ0FBQ2dELFlBQVksUUFBUXpDLEtBQUswQyxFQUFFLEVBQUUsUUFBUTtnQ0FDckRDLE9BQU8zQyxLQUFLMkMsS0FBSztnQ0FDakJDLFVBQVU1QyxFQUFBQSxtQkFBQUEsS0FBSzZDLFVBQVUsY0FBZjdDLHVDQUFBQSxnQkFBaUIsQ0FBQyxFQUFFLEtBQUk7Z0NBQ2xDdUQsV0FBV0o7Z0NBQ1hYLFVBQVU1QixDQUFBQSw0QkFBQUEsc0NBQUFBLGdCQUFpQjRDLG9CQUFvQixLQUFJO2dDQUNuREMsV0FBVy9COzRCQUNiO3dCQUNGO29CQUNGO2dCQUNGOztRQUNGOzZCQUFHO1FBQUM5QixPQUFPMEMsSUFBSTtRQUFFOUI7S0FBVTtJQUUzQixxQ0FBcUM7SUFDckNqRCxnREFBU0E7OEJBQUM7WUFDUixJQUFJLENBQUN5QyxRQUFRLENBQUNELFdBQVcyRCxPQUFPLEVBQUU7WUFFbEMsTUFBTUMsVUFBVTVELFdBQVcyRCxPQUFPO1lBQ2xDLE1BQU1FLGNBQWNELFFBQVFFLFlBQVk7WUFFeEMsTUFBTUM7bURBQWU7b0JBQ25CLE1BQU1DLFdBQVdKLFFBQVFLLFNBQVM7b0JBQ2xDLE1BQU1SLHVCQUF1QkosS0FBS2EsR0FBRyxDQUFDLEtBQUtiLEtBQUtDLEtBQUssQ0FBQyxXQUFhTyxDQUFBQSxjQUFjRCxRQUFRTyxZQUFZLElBQUs7b0JBRTFHLE1BQU0xQixXQUFXO3dCQUNmdUI7d0JBQ0FIO3dCQUNBTyxZQUFZLElBQUlyQixPQUFPc0IsV0FBVzt3QkFDbENaLHNCQUFzQmEsTUFBTWIsd0JBQXdCLElBQUlBO29CQUMxRDtvQkFFQTNDLG1CQUFtQjJCO29CQUVuQixJQUFJO3dCQUNGLE1BQU01RCx5RUFBbUJBLENBQUNvQixLQUFLMEMsRUFBRSxFQUFFRjt3QkFFbkMsMERBQTBEO3dCQUMxRCxJQUFJQSxTQUFTZ0Isb0JBQW9CLEdBQUcsT0FBTyxHQUFHO2dDQUloQ3hEOzRCQUhaLE1BQU15QyxhQUFhOzRCQUNuQmhELGtFQUFpQkEsQ0FBQ2dELFlBQVksUUFBUXpDLEtBQUswQyxFQUFFLEVBQUUsUUFBUTtnQ0FDckRDLE9BQU8zQyxLQUFLMkMsS0FBSztnQ0FDakJDLFVBQVU1QyxFQUFBQSxtQkFBQUEsS0FBSzZDLFVBQVUsY0FBZjdDLHVDQUFBQSxnQkFBaUIsQ0FBQyxFQUFFLEtBQUk7Z0NBQ2xDd0MsVUFBVUEsU0FBU2dCLG9CQUFvQjtnQ0FDdkNDLFdBQVcvQjs0QkFDYjs0QkFFQSxpQ0FBaUM7NEJBQ2pDLElBQUljLFNBQVNnQixvQkFBb0IsSUFBSSxJQUFJO29DQUczQnhEO2dDQUZaUCxrRUFBaUJBLENBQUNnRCxZQUFZLFlBQVl6QyxLQUFLMEMsRUFBRSxFQUFFLFFBQVE7b0NBQ3pEQyxPQUFPM0MsS0FBSzJDLEtBQUs7b0NBQ2pCQyxVQUFVNUMsRUFBQUEsb0JBQUFBLEtBQUs2QyxVQUFVLGNBQWY3Qyx3Q0FBQUEsaUJBQWlCLENBQUMsRUFBRSxLQUFJO2dDQUNwQzs0QkFDRjt3QkFDRjtvQkFDRixFQUFFLE9BQU9JLE9BQU87d0JBQ2Q2QyxRQUFRN0MsS0FBSyxDQUFDLGtDQUFrQ0E7b0JBQ2xEO2dCQUNGOztZQUVBLHNDQUFzQztZQUN0QyxJQUFJa0U7WUFDSixNQUFNQzttREFBZTtvQkFDbkJDLGFBQWFGO29CQUNiQSxVQUFVRyxXQUFXWCxjQUFjO2dCQUNyQzs7WUFFQUgsUUFBUTFCLGdCQUFnQixDQUFDLFVBQVVzQztZQUVuQyw4Q0FBOEM7WUFDOUMsSUFBSTNELDRCQUFBQSxzQ0FBQUEsZ0JBQWlCbUQsUUFBUSxFQUFFO2dCQUM3QkosUUFBUUssU0FBUyxHQUFHcEQsZ0JBQWdCbUQsUUFBUTtZQUM5QztZQUVBLHNDQUFzQztZQUN0QyxNQUFNVzt5REFBcUI7b0JBQ3pCWjtnQkFDRjs7WUFFQTlCLE9BQU9DLGdCQUFnQixDQUFDLGdCQUFnQnlDO1lBRXhDO3NDQUFPO29CQUNMZixRQUFRekIsbUJBQW1CLENBQUMsVUFBVXFDO29CQUN0Q3ZDLE9BQU9FLG1CQUFtQixDQUFDLGdCQUFnQndDO29CQUMzQ0YsYUFBYUY7b0JBQ2JSO2dCQUNGOztRQUNGOzZCQUFHO1FBQUM5RDtRQUFNWSw0QkFBQUEsc0NBQUFBLGdCQUFpQm1ELFFBQVE7UUFBRXJDO0tBQWlCO0lBRXRELG9DQUFvQztJQUNwQ25FLGdEQUFTQTs4QkFBQztZQUNSLE1BQU1vSDtzREFBa0I7b0JBQ3RCLE1BQU1DLFlBQVk1QyxPQUFPNkMsWUFBWTtvQkFDckMsSUFBSUQsYUFBYSxDQUFDQSxVQUFVRSxXQUFXLEVBQUU7d0JBQ3ZDekQsMEJBQTBCdUQsVUFBVUcsUUFBUTtvQkFDOUM7Z0JBQ0Y7O1lBRUFDLFNBQVMvQyxnQkFBZ0IsQ0FBQyxXQUFXMEM7WUFFckM7c0NBQU87b0JBQ0xLLFNBQVM5QyxtQkFBbUIsQ0FBQyxXQUFXeUM7Z0JBQzFDOztRQUNGOzZCQUFHLEVBQUU7SUFFTCxNQUFNTSxrQkFBa0I7UUFDdEJDLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxPQUFPO0lBQ1Q7SUFFQSxNQUFNQyw0QkFBNEIsQ0FBQ0M7UUFDakMzRSxzQkFBc0IyRTtRQUV0QixvREFBb0Q7UUFDcEQsSUFBSUEsYUFBYXRGLE1BQU07Z0JBSVRBO1lBSFosTUFBTXlDLGFBQWE7WUFDbkJoRCxrRUFBaUJBLENBQUNnRCxZQUFZLFlBQVl6QyxLQUFLMEMsRUFBRSxFQUFFLFFBQVE7Z0JBQ3pEQyxPQUFPM0MsS0FBSzJDLEtBQUs7Z0JBQ2pCQyxVQUFVNUMsRUFBQUEsbUJBQUFBLEtBQUs2QyxVQUFVLGNBQWY3Qyx1Q0FBQUEsZ0JBQWlCLENBQUMsRUFBRSxLQUFJO1lBQ3BDO1FBQ0Y7SUFDRjtJQUVBLE1BQU11Rix5QkFBeUIsQ0FBQ0M7UUFDOUJqRSxnQkFBZ0JpRTtJQUNsQjtJQUVBLGlEQUFpRDtJQUNqRCxNQUFNQyxlQUFlO1FBQ25CLElBQUksQ0FBQ3pGLE1BQU0sT0FBTztRQUVsQixrQ0FBa0M7UUFDbEMsTUFBTTBGLE9BQU8xRixLQUFLMkYsT0FBTyxDQUFDQyxPQUFPLENBQUMsWUFBWTtRQUM5QyxPQUFPRixLQUFLRyxLQUFLLENBQUMsT0FBT0MsTUFBTTtJQUNqQztJQUVBLElBQUk1RixXQUFXO1FBQ2IscUJBQ0UsOERBQUM2RjtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ3ZJLGtEQUFJQTtvQkFBQ3dJLE1BQUs7b0JBQVNELFdBQVU7O3NDQUM1Qiw4REFBQy9ILHVKQUFXQTs0QkFBQytILFdBQVU7Ozs7OztzQ0FDdkIsOERBQUNFO3NDQUFLOzs7Ozs7Ozs7Ozs7OEJBR1IsOERBQUNIO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQzVILHVKQUFPQTs0QkFBQzRILFdBQVU7Ozs7OztzQ0FDbkIsOERBQUNHOzRCQUFFSCxXQUFVO3NDQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJNUI7SUFFQSxJQUFJNUYsU0FBUyxDQUFDSixNQUFNO1FBQ2xCLHFCQUNFLDhEQUFDK0Y7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUN2SSxrREFBSUE7b0JBQUN3SSxNQUFLO29CQUFTRCxXQUFVOztzQ0FDNUIsOERBQUMvSCx1SkFBV0E7NEJBQUMrSCxXQUFVOzs7Ozs7c0NBQ3ZCLDhEQUFDRTtzQ0FBSzs7Ozs7Ozs7Ozs7OzhCQUdSLDhEQUFDSDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNHOzRCQUFFSCxXQUFVO3NDQUF5QjVGLFNBQVM7Ozs7OztzQ0FDL0MsOERBQUN6Qyx5REFBTUE7NEJBQUN5SSxTQUFROzRCQUFVQyxTQUFTLElBQU14RyxPQUFPeUcsSUFBSSxDQUFDO3NDQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNeEU7SUFFQSxxQkFDRSw4REFBQ1A7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ3ZJLGtEQUFJQTs0QkFBQ3dJLE1BQUs7NEJBQVNELFdBQVU7OzhDQUM1Qiw4REFBQy9ILHVKQUFXQTtvQ0FBQytILFdBQVU7Ozs7Ozs4Q0FDdkIsOERBQUNFOzhDQUFLOzs7Ozs7Ozs7Ozs7c0NBR1IsOERBQUNIOzRCQUFJQyxXQUFVO3NDQUNaeEYsMEJBQ0MsOERBQUN1RjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUM3SCx1SkFBT0E7d0NBQUM2SCxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7cURBSXRDLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUM5SCx1SkFBSUE7d0NBQUM4SCxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBT3hDcEYsaUNBQW1CLDhEQUFDNUIsaUZBQWtCQTtvQkFBQ3dELFVBQVU1QixnQkFBZ0I0QyxvQkFBb0I7Ozs7Ozs4QkFFdEYsOERBQUN1QztvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEOzswREFDQyw4REFBQ1E7Z0RBQ0NDLEtBQUt4RyxLQUFLeUcsUUFBUSxJQUFJO2dEQUN0QkMsS0FBSyxHQUFjLE9BQVgxRyxLQUFLMkMsS0FBSyxFQUFDO2dEQUNuQnFELFdBQVU7Ozs7OzswREFHWiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDVzt3REFBR1gsV0FBVTtrRUFBc0JoRyxLQUFLMkMsS0FBSzs7Ozs7O2tFQUM5Qyw4REFBQ3dEO3dEQUFFSCxXQUFVO2tFQUF5QmhHLEtBQUs0RyxNQUFNOzs7Ozs7a0VBRWpELDhEQUFDYjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0U7d0VBQUtGLFdBQVU7a0ZBQThCaEcsS0FBSzZHLE1BQU07Ozs7OztrRkFDekQsOERBQUNDO3dFQUNDQyxPQUFNO3dFQUNOQyxPQUFNO3dFQUNOQyxRQUFPO3dFQUNQQyxTQUFRO3dFQUNSQyxNQUFLO3dFQUNMQyxRQUFPO3dFQUNQcEIsV0FBVTtrRkFFViw0RUFBQ3FCOzRFQUFRQyxRQUFPOzs7Ozs7Ozs7Ozs7Ozs7OzswRUFHcEIsOERBQUN2QjswRUFDQyw0RUFBQ0c7O3dFQUFNbEcsS0FBS3VILEtBQUs7d0VBQUM7Ozs7Ozs7Ozs7OzswRUFFcEIsOERBQUN4QjswRUFDQyw0RUFBQ0c7OEVBQU1sRyxLQUFLd0gsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBSXhCLDhEQUFDekI7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDdkgsbUVBQVlBO2dFQUNYZ0osUUFBUXpILEtBQUswQyxFQUFFO2dFQUNmZ0YsV0FBVzFILEtBQUsyQyxLQUFLO2dFQUNyQmdGLFlBQVk7d0VBS0UzSDtvRUFKWiwwQkFBMEI7b0VBQzFCLE1BQU15QyxhQUFhO29FQUNuQmhELGtFQUFpQkEsQ0FBQ2dELFlBQVksWUFBWXpDLEtBQUswQyxFQUFFLEVBQUUsUUFBUTt3RUFDekRDLE9BQU8zQyxLQUFLMkMsS0FBSzt3RUFDakJDLFVBQVU1QyxFQUFBQSxtQkFBQUEsS0FBSzZDLFVBQVUsY0FBZjdDLHVDQUFBQSxnQkFBaUIsQ0FBQyxFQUFFLEtBQUk7b0VBQ3BDO2dFQUNGOzs7Ozs7MEVBRUYsOERBQUN0Qiw4RUFBaUJBO2dFQUFDc0IsTUFBTUE7Z0VBQU00SCxnQkFBZ0J2Qzs7Ozs7OzBFQUMvQyw4REFBQy9GLHNFQUFhQTtnRUFDWnVJLFdBQVc3SCxLQUFLMEMsRUFBRTtnRUFDbEJvRixjQUFjOUgsS0FBSzJDLEtBQUs7Z0VBQ3hCb0YsYUFBWTtnRUFDWkMsT0FBTzVHLDBCQUEwQjZHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBTXhDL0csY0FBYywyQkFBYSw4REFBQzdCLG1GQUFtQkE7d0NBQUN3SSxXQUFXN0gsS0FBSzBDLEVBQUU7d0NBQUV3RixlQUFlekM7Ozs7OztrREFFcEYsOERBQUNqRyx3RkFBY0E7d0NBQUNxSSxXQUFXN0gsS0FBSzBDLEVBQUU7d0NBQUVxRixhQUFZO3dDQUFPRCxjQUFjOUgsS0FBSzJDLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUluRiw4REFBQ29EOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDbkkscURBQUlBO2dDQUFDc0ssY0FBYTtnQ0FBVUMsT0FBT2xIO2dDQUFXbUgsZUFBZWxIO2dDQUFjNkUsV0FBVTs7a0RBQ3BGLDhEQUFDakkseURBQVFBO3dDQUFDaUksV0FBVTs7MERBQ2xCLDhEQUFDaEksNERBQVdBO2dEQUFDb0ssT0FBTTtnREFBVXBDLFdBQVU7O2tFQUNyQyw4REFBQzNILHVKQUFRQTt3REFBQzJILFdBQVU7Ozs7OztrRUFDcEIsOERBQUNFO2tFQUFLOzs7Ozs7Ozs7Ozs7MERBRVIsOERBQUNsSSw0REFBV0E7Z0RBQUNvSyxPQUFNO2dEQUFRcEMsV0FBVTs7a0VBQ25DLDhEQUFDekgsdUpBQVFBO3dEQUFDeUgsV0FBVTs7Ozs7O2tFQUNwQiw4REFBQ0U7a0VBQUs7Ozs7Ozs7Ozs7OzswREFFUiw4REFBQ2xJLDREQUFXQTtnREFBQ29LLE9BQU07Z0RBQU9wQyxXQUFVOztrRUFDbEMsOERBQUMxSCx1SkFBU0E7d0RBQUMwSCxXQUFVOzs7Ozs7a0VBQ3JCLDhEQUFDRTtrRUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUlWLDhEQUFDcEksNERBQVdBO3dDQUFDc0ssT0FBTTtrREFDakIsNEVBQUN4SyxxREFBSUE7NENBQUNvSSxXQUFVOzs4REFDZCw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNySSx5REFBTUE7b0VBQ0x5SSxTQUFTOUYsYUFBYSxVQUFVLFlBQVk7b0VBQzVDZ0ksTUFBSztvRUFDTHRDLFdBQVU7b0VBQ1ZLLFNBQVMsSUFBTTlGLFlBQVk7OEVBQzVCOzs7Ozs7OEVBR0QsOERBQUM1Qyx5REFBTUE7b0VBQ0x5SSxTQUFTOUYsYUFBYSxXQUFXLFlBQVk7b0VBQzdDZ0ksTUFBSztvRUFDTHRDLFdBQVU7b0VBQ1ZLLFNBQVMsSUFBTTlGLFlBQVk7OEVBQzVCOzs7Ozs7OEVBR0QsOERBQUM1Qyx5REFBTUE7b0VBQ0x5SSxTQUFTOUYsYUFBYSxVQUFVLFlBQVk7b0VBQzVDZ0ksTUFBSztvRUFDTHRDLFdBQVU7b0VBQ1ZLLFNBQVMsSUFBTTlGLFlBQVk7OEVBQzVCOzs7Ozs7Ozs7Ozs7c0VBS0gsOERBQUN3Rjs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNySSx5REFBTUE7b0VBQ0x5SSxTQUFTcEYsaUJBQWlCLFlBQVk7b0VBQ3RDc0gsTUFBSztvRUFDTHRDLFdBQVU7b0VBQ1ZLLFNBQVMsSUFBTXBGLGtCQUFrQixDQUFDRDs7c0ZBRWxDLDhEQUFDOEY7NEVBQ0NDLE9BQU07NEVBQ05DLE9BQU07NEVBQ05DLFFBQU87NEVBQ1BDLFNBQVE7NEVBQ1JDLE1BQUs7NEVBQ0xDLFFBQU87NEVBQ1BtQixhQUFZOzRFQUNaQyxlQUFjOzRFQUNkQyxnQkFBZTs7OEZBRWYsOERBQUNDO29GQUFLQyxHQUFFOzs7Ozs7OEZBQ1IsOERBQUNEO29GQUFLQyxHQUFFOzs7Ozs7Ozs7Ozs7c0ZBRVYsOERBQUN6QztzRkFBSzs7Ozs7Ozs7Ozs7OzhFQUdSLDhEQUFDdkkseURBQU1BO29FQUNMeUksU0FBU3RGLFlBQVksWUFBWTtvRUFDakN3SCxNQUFLO29FQUNMdEMsV0FBVTtvRUFDVkssU0FBUzt3RUFDUHRGLGFBQWEsQ0FBQ0Q7d0VBRWQsK0JBQStCO3dFQUMvQixJQUFJLENBQUNBLGFBQWFkLE1BQU07Z0ZBSVZBOzRFQUhaLE1BQU15QyxhQUFhOzRFQUNuQmhELGtFQUFpQkEsQ0FBQ2dELFlBQVksZUFBZXpDLEtBQUswQyxFQUFFLEVBQUUsUUFBUTtnRkFDNURDLE9BQU8zQyxLQUFLMkMsS0FBSztnRkFDakJDLFVBQVU1QyxFQUFBQSxtQkFBQUEsS0FBSzZDLFVBQVUsY0FBZjdDLHVDQUFBQSxnQkFBaUIsQ0FBQyxFQUFFLEtBQUk7NEVBQ3BDO3dFQUNGO29FQUNGOztzRkFFQSw4REFBQ3hCLHVKQUFhQTs0RUFBQ3dILFdBQVU7Ozs7OztzRkFDekIsOERBQUNFO3NGQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBS1osOERBQUNIO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVcsVUFBa0MsT0FBeEJsRixZQUFZLFNBQVM7c0VBQzdDLDRFQUFDakQscURBQUlBO2dFQUFDc0ssY0FBYTtnRUFBVW5DLFdBQVU7O2tGQUNyQyw4REFBQ2pJLHlEQUFRQTt3RUFBQ2lJLFdBQVU7OzBGQUNsQiw4REFBQ2hJLDREQUFXQTtnRkFBQ29LLE9BQU07Z0ZBQVVwQyxXQUFVOzBGQUFlOzs7Ozs7MEZBR3RELDhEQUFDaEksNERBQVdBO2dGQUFDb0ssT0FBTTtnRkFBZXBDLFdBQVU7MEZBQWU7Ozs7OzswRkFHM0QsOERBQUNoSSw0REFBV0E7Z0ZBQUNvSyxPQUFNO2dGQUFlcEMsV0FBVTswRkFBZTs7Ozs7Ozs7Ozs7O2tGQUs3RCw4REFBQ0Q7d0VBQUk2QyxLQUFLN0k7d0VBQVlpRyxXQUFVOzswRkFDOUIsOERBQUNsSSw0REFBV0E7Z0ZBQUNzSyxPQUFNO2dGQUFVcEMsV0FBVyxHQUE2QixPQUExQmYsZUFBZSxDQUFDM0UsU0FBUyxFQUFDOzBGQUNuRSw0RUFBQ3lGO29GQUFJOEMseUJBQXlCO3dGQUFFQyxRQUFROUksS0FBSzJGLE9BQU87b0ZBQUM7Ozs7Ozs7Ozs7OzBGQUd2RCw4REFBQzdILDREQUFXQTtnRkFBQ3NLLE9BQU07MEZBQ2pCLDRFQUFDckM7b0ZBQUlDLFdBQVcsR0FBNkIsT0FBMUJmLGVBQWUsQ0FBQzNFLFNBQVMsRUFBQzs4RkFDM0MsNEVBQUN5Rjt3RkFBSThDLHlCQUF5Qjs0RkFBRUMsUUFBUTlJLEtBQUsrSSxXQUFXLElBQUkvSSxLQUFLMkYsT0FBTzt3RkFBQzs7Ozs7Ozs7Ozs7Ozs7OzswRkFJN0UsOERBQUM3SCw0REFBV0E7Z0ZBQUNzSyxPQUFNOzBGQUNqQiw0RUFBQ3JDO29GQUFJQyxXQUFXLEdBQTZCLE9BQTFCZixlQUFlLENBQUMzRSxTQUFTLEVBQUM7OEZBQzNDLDRFQUFDeUY7d0ZBQUk4Qyx5QkFBeUI7NEZBQUVDLFFBQVE5SSxLQUFLZ0osWUFBWSxJQUFJO3dGQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7d0RBT3pFbEksMkJBQ0MsOERBQUNpRjs0REFBSUMsV0FBVTtzRUFDYiw0RUFBQzlHLGdFQUFVQTtnRUFBQzJJLFdBQVc3SCxLQUFLMEMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0RBS25DMUIsZ0NBQ0MsOERBQUMrRTtvREFBSUMsV0FBVTs4REFDYiw0RUFBQy9HLDhFQUFpQkE7d0RBQUM0SSxXQUFXN0gsS0FBSzBDLEVBQUU7Ozs7Ozs7Ozs7O2dEQUl4QyxDQUFDcEIsOEJBQ0EsOERBQUN5RTtvREFBSUMsV0FBVTs4REFDYiw0RUFBQzdHLGtGQUFrQkE7d0RBQ2pCdUcsTUFBTTFGLEtBQUsyRixPQUFPLENBQUNDLE9BQU8sQ0FBQyxZQUFZO3dEQUN2Q3FELGlCQUFpQjFEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU8zQiw4REFBQ3pILDREQUFXQTt3Q0FBQ3NLLE9BQU07a0RBQ2pCLDRFQUFDckM7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDcEkscURBQUlBO29EQUFDb0ksV0FBVTs7c0VBQ2QsOERBQUNrRDs0REFBR2xELFdBQVU7c0VBQXlCOzs7Ozs7c0VBQ3ZDLDhEQUFDNUcsNEVBQWdCQTs0REFBQ3lJLFdBQVc3SCxLQUFLMEMsRUFBRTs0REFBRW9GLGNBQWM5SCxLQUFLMkMsS0FBSzs7Ozs7Ozs7Ozs7OzhEQUdoRSw4REFBQy9FLHFEQUFJQTtvREFBQ29JLFdBQVU7O3NFQUNkLDhEQUFDa0Q7NERBQUdsRCxXQUFVO3NFQUF5Qjs7Ozs7O3NFQUN2Qyw4REFBQzdHLGtGQUFrQkE7NERBQ2pCdUcsTUFBTTFGLEtBQUsyRixPQUFPLENBQUNDLE9BQU8sQ0FBQyxZQUFZOzREQUN2Q3FELGlCQUFpQjFEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFNekIsOERBQUN6SCw0REFBV0E7d0NBQUNzSyxPQUFNO2tEQUNqQiw0RUFBQ3hLLHFEQUFJQTs0Q0FBQ29JLFdBQVU7OzhEQUNkLDhEQUFDa0Q7b0RBQUdsRCxXQUFVOzhEQUF5Qjs7Ozs7OzhEQUN2Qyw4REFBQ3pHLDhFQUFpQkE7b0RBQ2hCc0ksV0FBVzdILEtBQUswQyxFQUFFO29EQUNsQm9GLGNBQWM5SCxLQUFLMkMsS0FBSztvREFDeEJvRixhQUFZO29EQUNab0IsZUFBZTs0REFLRG5KO3dEQUpaLDhCQUE4Qjt3REFDOUIsTUFBTXlDLGFBQWE7d0RBQ25CaEQsa0VBQWlCQSxDQUFDZ0QsWUFBWSxnQkFBZ0J6QyxLQUFLMEMsRUFBRSxFQUFFLFFBQVE7NERBQzdEQyxPQUFPM0MsS0FBSzJDLEtBQUs7NERBQ2pCQyxVQUFVNUMsRUFBQUEsbUJBQUFBLEtBQUs2QyxVQUFVLGNBQWY3Qyx1Q0FBQUEsZ0JBQWlCLENBQUMsRUFBRSxLQUFJO3dEQUNwQztvREFDRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVXBCO0dBbmpCd0JMOztRQUNQakMsc0RBQVNBO1FBQ05xQix1REFBUUE7OztLQUZKWSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcRGVza3RvcFxcUHJvamVjdHNcXEFzdGV3YWlcXFRpbGtUZWJlYlxcdGlsa3RiZWIgKDEpXFxhcHBcXGJvb2tzXFxbc2x1Z11cXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXHJcblxyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tIFwicmVhY3RcIlxyXG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCJcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiXHJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCJcclxuaW1wb3J0IHsgQ2FyZCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiXHJcbmltcG9ydCB7IFRhYnMsIFRhYnNDb250ZW50LCBUYWJzTGlzdCwgVGFic1RyaWdnZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3RhYnNcIlxyXG5pbXBvcnQgeyBDaGV2cm9uTGVmdCwgV2lmaSwgV2lmaU9mZiwgTG9hZGVyMiwgQm9va09wZW4sIExpZ2h0YnVsYiwgU3BhcmtsZXMsIE1lc3NhZ2VTcXVhcmUgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcclxuaW1wb3J0IHsgQm9va0Jvb2ttYXJrIH0gZnJvbSBcIkAvY29tcG9uZW50cy9ib29rLWJvb2ttYXJrXCJcclxuaW1wb3J0IHsgT2ZmbGluZUJvb2tUb2dnbGUgfSBmcm9tIFwiQC9jb21wb25lbnRzL29mZmxpbmUtYm9vay10b2dnbGVcIlxyXG5pbXBvcnQgeyBnZXRPZmZsaW5lQm9vaywgc2F2ZVJlYWRpbmdQcm9ncmVzcywgZ2V0UmVhZGluZ1Byb2dyZXNzLCBpc0Jvb2tBdmFpbGFibGVPZmZsaW5lIH0gZnJvbSBcIkAvbGliL29mZmxpbmUtc3RvcmFnZVwiXHJcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSBcIkAvaG9va3MvdXNlLXRvYXN0XCJcclxuaW1wb3J0IHR5cGUgeyBCb29rIH0gZnJvbSBcIkAvdHlwZXMvYm9va1wiXHJcbmltcG9ydCB7IFJlYWRpbmdQcm9ncmVzc0JhciB9IGZyb20gXCJAL2NvbXBvbmVudHMvcmVhZGluZy1wcm9ncmVzcy1iYXJcIlxyXG5pbXBvcnQgeyBIaWdobGlnaHRDb250cm9scyB9IGZyb20gXCJAL2NvbXBvbmVudHMvaGlnaGxpZ2h0LWNvbnRyb2xzXCJcclxuaW1wb3J0IHsgTm90ZXNQYW5lbCB9IGZyb20gXCJAL2NvbXBvbmVudHMvbm90ZXMtcGFuZWxcIlxyXG5pbXBvcnQgeyBUZXh0VG9TcGVlY2hQbGF5ZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3RleHQtdG8tc3BlZWNoLXBsYXllclwiXHJcbmltcG9ydCB7IEZsYXNoY2FyZENyZWF0b3IgfSBmcm9tIFwiQC9jb21wb25lbnRzL2ZsYXNoY2FyZC1jcmVhdG9yXCJcclxuaW1wb3J0IHsgUmVhZGluZ1NwZWVkVHJhY2tlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvcmVhZGluZy1zcGVlZC10cmFja2VyXCJcclxuaW1wb3J0IHsgU29jaWFsU2hhcmluZyB9IGZyb20gXCJAL2NvbXBvbmVudHMvc29jaWFsLXNoYXJpbmdcIlxyXG5pbXBvcnQgeyBDb21wcmVoZW5zaW9uUXVpeiB9IGZyb20gXCJAL2NvbXBvbmVudHMvY29tcHJlaGVuc2lvbi1xdWl6XCJcclxuaW1wb3J0IHsgU2ltaWxhckNvbnRlbnQgfSBmcm9tIFwiQC9jb21wb25lbnRzL3JlY29tbWVuZGF0aW9ucy9zaW1pbGFyLWNvbnRlbnRcIlxyXG5pbXBvcnQgeyB0cmFja1VzZXJBY3Rpdml0eSB9IGZyb20gXCJAL2xpYi9hbmFseXRpY3NcIlxyXG5pbXBvcnQgeyBhcGksIGhhbmRsZUFwaUVycm9yIH0gZnJvbSBcIkAvbGliL2FwaVwiXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBCb29rUGFnZSh7IHBhcmFtcyB9OiB7IHBhcmFtczogeyBzbHVnOiBzdHJpbmcgfSB9KSB7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcclxuICBjb25zdCB7IHRvYXN0IH0gPSB1c2VUb2FzdCgpXHJcbiAgY29uc3QgY29udGVudFJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbClcclxuICBjb25zdCBbYm9vaywgc2V0Qm9va10gPSB1c2VTdGF0ZTxCb29rIHwgbnVsbD4obnVsbClcclxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcclxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXHJcbiAgY29uc3QgW2ZvbnRTaXplLCBzZXRGb250U2l6ZV0gPSB1c2VTdGF0ZTxcInNtYWxsXCIgfCBcIm1lZGl1bVwiIHwgXCJsYXJnZVwiPihcIm1lZGl1bVwiKVxyXG4gIGNvbnN0IFtpc09mZmxpbmUsIHNldElzT2ZmbGluZV0gPSB1c2VTdGF0ZShmYWxzZSlcclxuICBjb25zdCBbaXNPZmZsaW5lQXZhaWxhYmxlLCBzZXRJc09mZmxpbmVBdmFpbGFibGVdID0gdXNlU3RhdGUoZmFsc2UpXHJcbiAgY29uc3QgW3JlYWRpbmdQcm9ncmVzcywgc2V0UmVhZGluZ1Byb2dyZXNzXSA9IHVzZVN0YXRlPHtcclxuICAgIHBvc2l0aW9uOiBudW1iZXJcclxuICAgIHRvdGFsTGVuZ3RoOiBudW1iZXJcclxuICAgIGxhc3RSZWFkQXQ6IHN0cmluZ1xyXG4gICAgY29tcGxldGlvblBlcmNlbnRhZ2U6IG51bWJlclxyXG4gIH0gfCBudWxsPihudWxsKVxyXG4gIGNvbnN0IFtzaG93Tm90ZXMsIHNldFNob3dOb3Rlc10gPSB1c2VTdGF0ZShmYWxzZSlcclxuICBjb25zdCBbaXNIaWdobGlnaHRpbmcsIHNldElzSGlnaGxpZ2h0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZShcInJlYWRpbmdcIilcclxuICBjb25zdCBbc2VsZWN0ZWRUZXh0Rm9yU2hhcmluZywgc2V0U2VsZWN0ZWRUZXh0Rm9yU2hhcmluZ10gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxyXG4gIGNvbnN0IFtpc1R0c1BsYXlpbmcsIHNldElzVHRzUGxheWluZ10gPSB1c2VTdGF0ZShmYWxzZSlcclxuICBjb25zdCBbcmVhZGluZ1N0YXJ0VGltZSwgc2V0UmVhZGluZ1N0YXJ0VGltZV0gPSB1c2VTdGF0ZTxEYXRlIHwgbnVsbD4obnVsbClcclxuICBjb25zdCBbcmVhZGluZ1Nlc3Npb25JZCwgc2V0UmVhZGluZ1Nlc3Npb25JZF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxyXG5cclxuICAvLyBDaGVjayBvbmxpbmUgc3RhdHVzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIHNldElzT2ZmbGluZSghbmF2aWdhdG9yLm9uTGluZSlcclxuXHJcbiAgICBjb25zdCBoYW5kbGVPbmxpbmUgPSAoKSA9PiBzZXRJc09mZmxpbmUoZmFsc2UpXHJcbiAgICBjb25zdCBoYW5kbGVPZmZsaW5lID0gKCkgPT4gc2V0SXNPZmZsaW5lKHRydWUpXHJcblxyXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJvbmxpbmVcIiwgaGFuZGxlT25saW5lKVxyXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJvZmZsaW5lXCIsIGhhbmRsZU9mZmxpbmUpXHJcblxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJvbmxpbmVcIiwgaGFuZGxlT25saW5lKVxyXG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcIm9mZmxpbmVcIiwgaGFuZGxlT2ZmbGluZSlcclxuICAgIH1cclxuICB9LCBbXSlcclxuXHJcbiAgLy8gRmV0Y2ggYm9vayBkYXRhXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGZldGNoQm9vayA9IGFzeW5jICgpID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBzZXRJc0xvYWRpbmcodHJ1ZSlcclxuXHJcbiAgICAgICAgLy8gRmlyc3QgY2hlY2sgaWYgYm9vayBpcyBhdmFpbGFibGUgb2ZmbGluZVxyXG4gICAgICAgIGNvbnN0IG9mZmxpbmVBdmFpbGFibGUgPSBhd2FpdCBpc0Jvb2tBdmFpbGFibGVPZmZsaW5lKHBhcmFtcy5zbHVnKVxyXG4gICAgICAgIHNldElzT2ZmbGluZUF2YWlsYWJsZShvZmZsaW5lQXZhaWxhYmxlKVxyXG5cclxuICAgICAgICBpZiAoaXNPZmZsaW5lICYmICFvZmZsaW5lQXZhaWxhYmxlKSB7XHJcbiAgICAgICAgICAvLyBJZiBvZmZsaW5lIGFuZCBib29rIG5vdCBhdmFpbGFibGUgb2ZmbGluZVxyXG4gICAgICAgICAgc2V0RXJyb3IoXHJcbiAgICAgICAgICAgIFwiWW91J3JlIG9mZmxpbmUgYW5kIHRoaXMgYm9vayBpc24ndCBhdmFpbGFibGUgb2ZmbGluZS4gUGxlYXNlIGNvbm5lY3QgdG8gdGhlIGludGVybmV0IG9yIGNob29zZSBhIGJvb2sgeW91J3ZlIHNhdmVkIGZvciBvZmZsaW5lIHJlYWRpbmcuXCIsXHJcbiAgICAgICAgICApXHJcbiAgICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXHJcbiAgICAgICAgICByZXR1cm5cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGxldCBib29rRGF0YTogQm9vayB8IG51bGwgPSBudWxsXHJcblxyXG4gICAgICAgIGlmIChvZmZsaW5lQXZhaWxhYmxlKSB7XHJcbiAgICAgICAgICAvLyBHZXQgZnJvbSBvZmZsaW5lIHN0b3JhZ2VcclxuICAgICAgICAgIGJvb2tEYXRhID0gYXdhaXQgZ2V0T2ZmbGluZUJvb2socGFyYW1zLnNsdWcpXHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIC8vIEZldGNoIGZyb20gQVBJXHJcbiAgICAgICAgICBib29rRGF0YSA9IGF3YWl0IGFwaS5nZXRCb29rQnlJZChwYXJhbXMuc2x1ZylcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHNldEJvb2soYm9va0RhdGEpXHJcblxyXG4gICAgICAgIC8vIExvYWQgcmVhZGluZyBwcm9ncmVzc1xyXG4gICAgICAgIGNvbnN0IHByb2dyZXNzID0gYXdhaXQgZ2V0UmVhZGluZ1Byb2dyZXNzKHBhcmFtcy5zbHVnKVxyXG4gICAgICAgIGlmIChwcm9ncmVzcykge1xyXG4gICAgICAgICAgc2V0UmVhZGluZ1Byb2dyZXNzKHByb2dyZXNzKVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gVHJhY2sgdmlldyBhY3Rpdml0eVxyXG4gICAgICAgIC8vIEluIGEgcmVhbCBhcHAsIHdlIHdvdWxkIGdldCB0aGUgdXNlciBJRCBmcm9tIGF1dGhlbnRpY2F0aW9uXHJcbiAgICAgICAgY29uc3QgbW9ja1VzZXJJZCA9IFwidXNlci0xMjNcIlxyXG4gICAgICAgIHRyYWNrVXNlckFjdGl2aXR5KG1vY2tVc2VySWQsIFwidmlld1wiLCBib29rRGF0YS5pZCwgXCJib29rXCIsIHtcclxuICAgICAgICAgIHRpdGxlOiBib29rRGF0YS50aXRsZSxcclxuICAgICAgICAgIGNhdGVnb3J5OiBib29rRGF0YS5jYXRlZ29yaWVzPy5bMF0gfHwgXCJVbmNhdGVnb3JpemVkXCIsXHJcbiAgICAgICAgfSlcclxuXHJcbiAgICAgICAgLy8gU3RhcnQgcmVhZGluZyBzZXNzaW9uXHJcbiAgICAgICAgc2V0UmVhZGluZ1N0YXJ0VGltZShuZXcgRGF0ZSgpKVxyXG4gICAgICAgIHNldFJlYWRpbmdTZXNzaW9uSWQoYHJlYWRpbmctc2Vzc2lvbi0ke0RhdGUubm93KCl9YClcclxuICAgICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIGJvb2s6XCIsIGVycilcclxuICAgICAgICBzZXRFcnJvcihcIkVycm9yIGxvYWRpbmcgYm9vayBkZXRhaWxzLiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLlwiKVxyXG4gICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGZldGNoQm9vaygpXHJcblxyXG4gICAgLy8gQ2xlYW51cCBmdW5jdGlvbiB0byB0cmFjayByZWFkaW5nIHRpbWUgd2hlbiBjb21wb25lbnQgdW5tb3VudHNcclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIGlmIChib29rICYmIHJlYWRpbmdTdGFydFRpbWUpIHtcclxuICAgICAgICBjb25zdCBlbmRUaW1lID0gbmV3IERhdGUoKVxyXG4gICAgICAgIGNvbnN0IHRpbWVTcGVudE1pbnV0ZXMgPSBNYXRoLnJvdW5kKChlbmRUaW1lLmdldFRpbWUoKSAtIHJlYWRpbmdTdGFydFRpbWUuZ2V0VGltZSgpKSAvIDYwMDAwKVxyXG5cclxuICAgICAgICBpZiAodGltZVNwZW50TWludXRlcyA+IDApIHtcclxuICAgICAgICAgIC8vIFRyYWNrIHJlYWQgYWN0aXZpdHkgd2l0aCB0aW1lIHNwZW50XHJcbiAgICAgICAgICBjb25zdCBtb2NrVXNlcklkID0gXCJ1c2VyLTEyM1wiXHJcbiAgICAgICAgICB0cmFja1VzZXJBY3Rpdml0eShtb2NrVXNlcklkLCBcInJlYWRcIiwgYm9vay5pZCwgXCJib29rXCIsIHtcclxuICAgICAgICAgICAgdGl0bGU6IGJvb2sudGl0bGUsXHJcbiAgICAgICAgICAgIGNhdGVnb3J5OiBib29rLmNhdGVnb3JpZXM/LlswXSB8fCBcIlVuY2F0ZWdvcml6ZWRcIixcclxuICAgICAgICAgICAgdGltZVNwZW50OiB0aW1lU3BlbnRNaW51dGVzLFxyXG4gICAgICAgICAgICBwcm9ncmVzczogcmVhZGluZ1Byb2dyZXNzPy5jb21wbGV0aW9uUGVyY2VudGFnZSB8fCAwLFxyXG4gICAgICAgICAgICBzZXNzaW9uSWQ6IHJlYWRpbmdTZXNzaW9uSWQsXHJcbiAgICAgICAgICB9KVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH0sIFtwYXJhbXMuc2x1ZywgaXNPZmZsaW5lXSlcclxuXHJcbiAgLy8gU2F2ZSByZWFkaW5nIHByb2dyZXNzIHBlcmlvZGljYWxseVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoIWJvb2sgfHwgIWNvbnRlbnRSZWYuY3VycmVudCkgcmV0dXJuXHJcblxyXG4gICAgY29uc3QgY29udGVudCA9IGNvbnRlbnRSZWYuY3VycmVudFxyXG4gICAgY29uc3QgdG90YWxMZW5ndGggPSBjb250ZW50LnNjcm9sbEhlaWdodFxyXG5cclxuICAgIGNvbnN0IHNhdmVQcm9ncmVzcyA9IGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgcG9zaXRpb24gPSBjb250ZW50LnNjcm9sbFRvcFxyXG4gICAgICBjb25zdCBjb21wbGV0aW9uUGVyY2VudGFnZSA9IE1hdGgubWluKDEwMCwgTWF0aC5yb3VuZCgocG9zaXRpb24gLyAodG90YWxMZW5ndGggLSBjb250ZW50LmNsaWVudEhlaWdodCkpICogMTAwKSlcclxuXHJcbiAgICAgIGNvbnN0IHByb2dyZXNzID0ge1xyXG4gICAgICAgIHBvc2l0aW9uLFxyXG4gICAgICAgIHRvdGFsTGVuZ3RoLFxyXG4gICAgICAgIGxhc3RSZWFkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcclxuICAgICAgICBjb21wbGV0aW9uUGVyY2VudGFnZTogaXNOYU4oY29tcGxldGlvblBlcmNlbnRhZ2UpID8gMCA6IGNvbXBsZXRpb25QZXJjZW50YWdlLFxyXG4gICAgICB9XHJcblxyXG4gICAgICBzZXRSZWFkaW5nUHJvZ3Jlc3MocHJvZ3Jlc3MpXHJcblxyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGF3YWl0IHNhdmVSZWFkaW5nUHJvZ3Jlc3MoYm9vay5pZCwgcHJvZ3Jlc3MpXHJcblxyXG4gICAgICAgIC8vIFRyYWNrIHByb2dyZXNzIHVwZGF0ZSBpZiBzaWduaWZpY2FudCBjaGFuZ2UgKGV2ZXJ5IDEwJSlcclxuICAgICAgICBpZiAocHJvZ3Jlc3MuY29tcGxldGlvblBlcmNlbnRhZ2UgJSAxMCA9PT0gMCkge1xyXG4gICAgICAgICAgY29uc3QgbW9ja1VzZXJJZCA9IFwidXNlci0xMjNcIlxyXG4gICAgICAgICAgdHJhY2tVc2VyQWN0aXZpdHkobW9ja1VzZXJJZCwgXCJyZWFkXCIsIGJvb2suaWQsIFwiYm9va1wiLCB7XHJcbiAgICAgICAgICAgIHRpdGxlOiBib29rLnRpdGxlLFxyXG4gICAgICAgICAgICBjYXRlZ29yeTogYm9vay5jYXRlZ29yaWVzPy5bMF0gfHwgXCJVbmNhdGVnb3JpemVkXCIsXHJcbiAgICAgICAgICAgIHByb2dyZXNzOiBwcm9ncmVzcy5jb21wbGV0aW9uUGVyY2VudGFnZSxcclxuICAgICAgICAgICAgc2Vzc2lvbklkOiByZWFkaW5nU2Vzc2lvbklkLFxyXG4gICAgICAgICAgfSlcclxuXHJcbiAgICAgICAgICAvLyBJZiBjb21wbGV0ZWQsIHRyYWNrIGNvbXBsZXRpb25cclxuICAgICAgICAgIGlmIChwcm9ncmVzcy5jb21wbGV0aW9uUGVyY2VudGFnZSA+PSA5MCkge1xyXG4gICAgICAgICAgICB0cmFja1VzZXJBY3Rpdml0eShtb2NrVXNlcklkLCBcImNvbXBsZXRlXCIsIGJvb2suaWQsIFwiYm9va1wiLCB7XHJcbiAgICAgICAgICAgICAgdGl0bGU6IGJvb2sudGl0bGUsXHJcbiAgICAgICAgICAgICAgY2F0ZWdvcnk6IGJvb2suY2F0ZWdvcmllcz8uWzBdIHx8IFwiVW5jYXRlZ29yaXplZFwiLFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3Igc2F2aW5nIHJlYWRpbmcgcHJvZ3Jlc3M6XCIsIGVycm9yKVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8gU2F2ZSBwcm9ncmVzcyBvbiBzY3JvbGwgKGRlYm91bmNlZClcclxuICAgIGxldCB0aW1lb3V0OiBOb2RlSlMuVGltZW91dFxyXG4gICAgY29uc3QgaGFuZGxlU2Nyb2xsID0gKCkgPT4ge1xyXG4gICAgICBjbGVhclRpbWVvdXQodGltZW91dClcclxuICAgICAgdGltZW91dCA9IHNldFRpbWVvdXQoc2F2ZVByb2dyZXNzLCA1MDApXHJcbiAgICB9XHJcblxyXG4gICAgY29udGVudC5hZGRFdmVudExpc3RlbmVyKFwic2Nyb2xsXCIsIGhhbmRsZVNjcm9sbClcclxuXHJcbiAgICAvLyBSZXN0b3JlIHNjcm9sbCBwb3NpdGlvbiBmcm9tIHNhdmVkIHByb2dyZXNzXHJcbiAgICBpZiAocmVhZGluZ1Byb2dyZXNzPy5wb3NpdGlvbikge1xyXG4gICAgICBjb250ZW50LnNjcm9sbFRvcCA9IHJlYWRpbmdQcm9ncmVzcy5wb3NpdGlvblxyXG4gICAgfVxyXG5cclxuICAgIC8vIFNhdmUgcHJvZ3Jlc3Mgd2hlbiBsZWF2aW5nIHRoZSBwYWdlXHJcbiAgICBjb25zdCBoYW5kbGVCZWZvcmVVbmxvYWQgPSAoKSA9PiB7XHJcbiAgICAgIHNhdmVQcm9ncmVzcygpXHJcbiAgICB9XHJcblxyXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJiZWZvcmV1bmxvYWRcIiwgaGFuZGxlQmVmb3JlVW5sb2FkKVxyXG5cclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIGNvbnRlbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcihcInNjcm9sbFwiLCBoYW5kbGVTY3JvbGwpXHJcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKFwiYmVmb3JldW5sb2FkXCIsIGhhbmRsZUJlZm9yZVVubG9hZClcclxuICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXQpXHJcbiAgICAgIHNhdmVQcm9ncmVzcygpXHJcbiAgICB9XHJcbiAgfSwgW2Jvb2ssIHJlYWRpbmdQcm9ncmVzcz8ucG9zaXRpb24sIHJlYWRpbmdTZXNzaW9uSWRdKVxyXG5cclxuICAvLyBIYW5kbGUgdGV4dCBzZWxlY3Rpb24gZm9yIHNoYXJpbmdcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgaGFuZGxlU2VsZWN0aW9uID0gKCkgPT4ge1xyXG4gICAgICBjb25zdCBzZWxlY3Rpb24gPSB3aW5kb3cuZ2V0U2VsZWN0aW9uKClcclxuICAgICAgaWYgKHNlbGVjdGlvbiAmJiAhc2VsZWN0aW9uLmlzQ29sbGFwc2VkKSB7XHJcbiAgICAgICAgc2V0U2VsZWN0ZWRUZXh0Rm9yU2hhcmluZyhzZWxlY3Rpb24udG9TdHJpbmcoKSlcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoXCJtb3VzZXVwXCIsIGhhbmRsZVNlbGVjdGlvbilcclxuXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKFwibW91c2V1cFwiLCBoYW5kbGVTZWxlY3Rpb24pXHJcbiAgICB9XHJcbiAgfSwgW10pXHJcblxyXG4gIGNvbnN0IGZvbnRTaXplQ2xhc3NlcyA9IHtcclxuICAgIHNtYWxsOiBcInRleHQtc20gbGVhZGluZy02XCIsXHJcbiAgICBtZWRpdW06IFwidGV4dC1iYXNlIGxlYWRpbmctN1wiLFxyXG4gICAgbGFyZ2U6IFwidGV4dC1sZyBsZWFkaW5nLThcIixcclxuICB9XHJcblxyXG4gIGNvbnN0IGhhbmRsZU9mZmxpbmVTdGF0dXNDaGFuZ2UgPSAoYXZhaWxhYmxlOiBib29sZWFuKSA9PiB7XHJcbiAgICBzZXRJc09mZmxpbmVBdmFpbGFibGUoYXZhaWxhYmxlKVxyXG5cclxuICAgIC8vIFRyYWNrIGRvd25sb2FkIGFjdGl2aXR5IGlmIG1hZGUgYXZhaWxhYmxlIG9mZmxpbmVcclxuICAgIGlmIChhdmFpbGFibGUgJiYgYm9vaykge1xyXG4gICAgICBjb25zdCBtb2NrVXNlcklkID0gXCJ1c2VyLTEyM1wiXHJcbiAgICAgIHRyYWNrVXNlckFjdGl2aXR5KG1vY2tVc2VySWQsIFwiZG93bmxvYWRcIiwgYm9vay5pZCwgXCJib29rXCIsIHtcclxuICAgICAgICB0aXRsZTogYm9vay50aXRsZSxcclxuICAgICAgICBjYXRlZ29yeTogYm9vay5jYXRlZ29yaWVzPy5bMF0gfHwgXCJVbmNhdGVnb3JpemVkXCIsXHJcbiAgICAgIH0pXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBjb25zdCBoYW5kbGVUdHNQbGF5aW5nQ2hhbmdlID0gKHBsYXlpbmc6IGJvb2xlYW4pID0+IHtcclxuICAgIHNldElzVHRzUGxheWluZyhwbGF5aW5nKVxyXG4gIH1cclxuXHJcbiAgLy8gRXN0aW1hdGUgd29yZCBjb3VudCBmb3IgcmVhZGluZyBzcGVlZCB0cmFja2luZ1xyXG4gIGNvbnN0IGdldFdvcmRDb3VudCA9ICgpID0+IHtcclxuICAgIGlmICghYm9vaykgcmV0dXJuIDBcclxuXHJcbiAgICAvLyBTdHJpcCBIVE1MIHRhZ3MgYW5kIGNvdW50IHdvcmRzXHJcbiAgICBjb25zdCB0ZXh0ID0gYm9vay5zdW1tYXJ5LnJlcGxhY2UoLzxbXj5dKj4vZywgXCJcIilcclxuICAgIHJldHVybiB0ZXh0LnNwbGl0KC9cXHMrLykubGVuZ3RoXHJcbiAgfVxyXG5cclxuICBpZiAoaXNMb2FkaW5nKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBweS04IG1kOnB5LTEyXCI+XHJcbiAgICAgICAgPExpbmsgaHJlZj1cIi9ib29rc1wiIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBnYXAtMSB0ZXh0LW11dGVkLWZvcmVncm91bmQgaG92ZXI6dGV4dC1mb3JlZ3JvdW5kIG1iLTZcIj5cclxuICAgICAgICAgIDxDaGV2cm9uTGVmdCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cclxuICAgICAgICAgIDxzcGFuPkJhY2sgdG8gQm9va3M8L3NwYW4+XHJcbiAgICAgICAgPC9MaW5rPlxyXG5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyIGgtNjRcIj5cclxuICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtOCB3LTggYW5pbWF0ZS1zcGluIHRleHQtcHJpbWFyeVwiIC8+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtbC0yXCI+TG9hZGluZyBib29rIGRldGFpbHMuLi48L3A+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKVxyXG4gIH1cclxuXHJcbiAgaWYgKGVycm9yIHx8ICFib29rKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBweS04IG1kOnB5LTEyXCI+XHJcbiAgICAgICAgPExpbmsgaHJlZj1cIi9ib29rc1wiIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBnYXAtMSB0ZXh0LW11dGVkLWZvcmVncm91bmQgaG92ZXI6dGV4dC1mb3JlZ3JvdW5kIG1iLTZcIj5cclxuICAgICAgICAgIDxDaGV2cm9uTGVmdCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cclxuICAgICAgICAgIDxzcGFuPkJhY2sgdG8gQm9va3M8L3NwYW4+XHJcbiAgICAgICAgPC9MaW5rPlxyXG5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWRlc3RydWN0aXZlIG1iLTRcIj57ZXJyb3IgfHwgXCJCb29rIG5vdCBmb3VuZFwifTwvcD5cclxuICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaChcIi9ib29rc1wiKX0+XHJcbiAgICAgICAgICAgIEJyb3dzZSBCb29rc1xyXG4gICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKVxyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuXCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIHB5LTggbWQ6cHktMTJcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi02XCI+XHJcbiAgICAgICAgICA8TGluayBocmVmPVwiL2Jvb2tzXCIgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGdhcC0xIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LWZvcmVncm91bmRcIj5cclxuICAgICAgICAgICAgPENoZXZyb25MZWZ0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgICA8c3Bhbj5CYWNrIHRvIEJvb2tzPC9zcGFuPlxyXG4gICAgICAgICAgPC9MaW5rPlxyXG5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAge2lzT2ZmbGluZSA/IChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHRleHQtYW1iZXItNjAwIGRhcms6dGV4dC1hbWJlci00MDAgYmctYW1iZXItNTAgZGFyazpiZy1hbWJlci05MDAvMjAgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzXCI+XHJcbiAgICAgICAgICAgICAgICA8V2lmaU9mZiBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxyXG4gICAgICAgICAgICAgICAgT2ZmbGluZSBNb2RlXHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LWdyZWVuLTYwMCBkYXJrOnRleHQtZ3JlZW4tNDAwIGJnLWdyZWVuLTUwIGRhcms6YmctZ3JlZW4tOTAwLzIwIHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14c1wiPlxyXG4gICAgICAgICAgICAgICAgPFdpZmkgY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cclxuICAgICAgICAgICAgICAgIE9ubGluZVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHtyZWFkaW5nUHJvZ3Jlc3MgJiYgPFJlYWRpbmdQcm9ncmVzc0JhciBwcm9ncmVzcz17cmVhZGluZ1Byb2dyZXNzLmNvbXBsZXRpb25QZXJjZW50YWdlfSAvPn1cclxuXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC04IG1iLThcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWQ6Y29sLXNwYW4tMVwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0aWNreSB0b3AtMjQgc3BhY2UteS02XCI+XHJcbiAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgIDxpbWdcclxuICAgICAgICAgICAgICAgICAgc3JjPXtib29rLmNvdmVyVXJsIHx8IFwiL3BsYWNlaG9sZGVyLnN2Z1wifVxyXG4gICAgICAgICAgICAgICAgICBhbHQ9e2Ake2Jvb2sudGl0bGV9IGJvb2sgY292ZXJgfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgbWF4LXctWzI0MHB4XSBteC1hdXRvIHJvdW5kZWQteGwgc2hhZG93LW1kIGNhcmQtdGhlbWVkXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02IHNwYWNlLXktNCBncmFkaWVudC10ZXh0LW92ZXJsYXkgcC00IHJvdW5kZWQteGxcIj5cclxuICAgICAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPntib29rLnRpdGxlfTwvaDE+XHJcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPntib29rLmF1dGhvcn08L3A+XHJcblxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC00IHRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5IGZvbnQtc2VtaWJvbGRcIj57Ym9vay5yYXRpbmd9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHN2Z1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg9XCIxNlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodD1cIjE2XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCJjdXJyZW50Q29sb3JcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2U9XCJub25lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zZWNvbmRhcnlcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8cG9seWdvbiBwb2ludHM9XCIxMiAyIDE1LjA5IDguMjYgMjIgOS4yNyAxNyAxNC4xNCAxOC4xOCAyMS4wMiAxMiAxNy43NyA1LjgyIDIxLjAyIDcgMTQuMTQgMiA5LjI3IDguOTEgOC4yNiAxMiAyXCI+PC9wb2x5Z29uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntib29rLnBhZ2VzfSBwYWdlczwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e2Jvb2subGFuZ3VhZ2V9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHQtNCBmbGV4IGZsZXgtd3JhcCBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxCb29rQm9va21hcmtcclxuICAgICAgICAgICAgICAgICAgICAgIGJvb2tJZD17Ym9vay5pZH1cclxuICAgICAgICAgICAgICAgICAgICAgIGJvb2tUaXRsZT17Ym9vay50aXRsZX1cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQm9va21hcms9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gVHJhY2sgYm9va21hcmsgYWN0aXZpdHlcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbW9ja1VzZXJJZCA9IFwidXNlci0xMjNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0cmFja1VzZXJBY3Rpdml0eShtb2NrVXNlcklkLCBcImJvb2ttYXJrXCIsIGJvb2suaWQsIFwiYm9va1wiLCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6IGJvb2sudGl0bGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2F0ZWdvcnk6IGJvb2suY2F0ZWdvcmllcz8uWzBdIHx8IFwiVW5jYXRlZ29yaXplZFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxPZmZsaW5lQm9va1RvZ2dsZSBib29rPXtib29rfSBvblN0YXR1c0NoYW5nZT17aGFuZGxlT2ZmbGluZVN0YXR1c0NoYW5nZX0gLz5cclxuICAgICAgICAgICAgICAgICAgICA8U29jaWFsU2hhcmluZ1xyXG4gICAgICAgICAgICAgICAgICAgICAgY29udGVudElkPXtib29rLmlkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgY29udGVudFRpdGxlPXtib29rLnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgY29udGVudFR5cGU9XCJib29rXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHF1b3RlPXtzZWxlY3RlZFRleHRGb3JTaGFyaW5nIHx8IHVuZGVmaW5lZH1cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICB7YWN0aXZlVGFiID09PSBcInJlYWRpbmdcIiAmJiA8UmVhZGluZ1NwZWVkVHJhY2tlciBjb250ZW50SWQ9e2Jvb2suaWR9IGNvbnRlbnRMZW5ndGg9e2dldFdvcmRDb3VudCgpfSAvPn1cclxuXHJcbiAgICAgICAgICAgICAgPFNpbWlsYXJDb250ZW50IGNvbnRlbnRJZD17Ym9vay5pZH0gY29udGVudFR5cGU9XCJib29rXCIgY29udGVudFRpdGxlPXtib29rLnRpdGxlfSAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWQ6Y29sLXNwYW4tMlwiPlxyXG4gICAgICAgICAgICA8VGFicyBkZWZhdWx0VmFsdWU9XCJyZWFkaW5nXCIgdmFsdWU9e2FjdGl2ZVRhYn0gb25WYWx1ZUNoYW5nZT17c2V0QWN0aXZlVGFifSBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cclxuICAgICAgICAgICAgICA8VGFic0xpc3QgY2xhc3NOYW1lPVwibWItNlwiPlxyXG4gICAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwicmVhZGluZ1wiIGNsYXNzTmFtZT1cImdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxCb29rT3BlbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4+UmVhZGluZzwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvVGFic1RyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJ0b29sc1wiIGNsYXNzTmFtZT1cImdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxTcGFya2xlcyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4+TGVhcm5pbmcgVG9vbHM8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L1RhYnNUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwicXVpelwiIGNsYXNzTmFtZT1cImdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxMaWdodGJ1bGIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuPlF1aXo8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L1RhYnNUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgIDwvVGFic0xpc3Q+XHJcblxyXG4gICAgICAgICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cInJlYWRpbmdcIj5cclxuICAgICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cInAtNiByb3VuZGVkLXhsIGJvcmRlci1ub25lIHNoYWRvdy1zbSBjYXJkLXRoZW1lZFwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9e2ZvbnRTaXplID09PSBcInNtYWxsXCIgPyBcImRlZmF1bHRcIiA6IFwib3V0bGluZVwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLWZ1bGwgaC04IHctOCBwLTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRGb250U2l6ZShcInNtYWxsXCIpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBTXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD17Zm9udFNpemUgPT09IFwibWVkaXVtXCIgPyBcImRlZmF1bHRcIiA6IFwib3V0bGluZVwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLWZ1bGwgaC04IHctOCBwLTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRGb250U2l6ZShcIm1lZGl1bVwiKX1cclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgTVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9e2ZvbnRTaXplID09PSBcImxhcmdlXCIgPyBcImRlZmF1bHRcIiA6IFwib3V0bGluZVwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLWZ1bGwgaC04IHctOCBwLTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRGb250U2l6ZShcImxhcmdlXCIpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBMXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9e2lzSGlnaGxpZ2h0aW5nID8gXCJkZWZhdWx0XCIgOiBcIm91dGxpbmVcIn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ2FwLTFcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc0hpZ2hsaWdodGluZyghaXNIaWdobGlnaHRpbmcpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3ZnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg9XCIxNlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PVwiMTZcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJtOSAxMS02IDZ2M2g5bDMtM1wiPjwvcGF0aD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwibTIyIDEyLTQuNiA0LjZhMiAyIDAgMCAxLTIuOCAwbC01LjItNS4yYTIgMiAwIDAgMSAwLTIuOEwxNCA0XCI+PC9wYXRoPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+SGlnaGxpZ2h0PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXtzaG93Tm90ZXMgPyBcImRlZmF1bHRcIiA6IFwib3V0bGluZVwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJnYXAtMVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTaG93Tm90ZXMoIXNob3dOb3RlcylcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gVHJhY2sgbm90ZSBjcmVhdGlvbiBhY3Rpdml0eVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlmICghc2hvd05vdGVzICYmIGJvb2spIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG1vY2tVc2VySWQgPSBcInVzZXItMTIzXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWNrVXNlckFjdGl2aXR5KG1vY2tVc2VySWQsIFwibm90ZV9jcmVhdGVcIiwgYm9vay5pZCwgXCJib29rXCIsIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6IGJvb2sudGl0bGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhdGVnb3J5OiBib29rLmNhdGVnb3JpZXM/LlswXSB8fCBcIlVuY2F0ZWdvcml6ZWRcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TWVzc2FnZVNxdWFyZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+Tm90ZXM8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXhcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXgtMSAke3Nob3dOb3RlcyA/IFwicHItNFwiIDogXCJcIn1gfT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJzIGRlZmF1bHRWYWx1ZT1cInN1bW1hcnlcIiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYnNMaXN0IGNsYXNzTmFtZT1cIm1iLTQgYmctbXV0ZWQvNTAgcC0xIHJvdW5kZWQtZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cInN1bW1hcnlcIiBjbGFzc05hbWU9XCJyb3VuZGVkLWZ1bGxcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFN1bW1hcnlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYnNUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cImtleS1pbnNpZ2h0c1wiIGNsYXNzTmFtZT1cInJvdW5kZWQtZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgS2V5IEluc2lnaHRzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJzVHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJhcHBsaWNhdGlvbnNcIiBjbGFzc05hbWU9XCJyb3VuZGVkLWZ1bGxcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIEFwcGxpY2F0aW9uc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFic1RyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFic0xpc3Q+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IHJlZj17Y29udGVudFJlZn0gY2xhc3NOYW1lPVwibWF4LWgtWzcwdmhdIG92ZXJmbG93LXktYXV0byBwci00IGN1c3RvbS1zY3JvbGxiYXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJzdW1tYXJ5XCIgY2xhc3NOYW1lPXtgJHtmb250U2l6ZUNsYXNzZXNbZm9udFNpemVdfSBzcGFjZS15LTRgfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3sgX19odG1sOiBib29rLnN1bW1hcnkgfX0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYnNDb250ZW50PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJrZXktaW5zaWdodHNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtmb250U2l6ZUNsYXNzZXNbZm9udFNpemVdfSBzcGFjZS15LTRgfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBkYW5nZXJvdXNseVNldElubmVySFRNTD17eyBfX2h0bWw6IGJvb2sua2V5SW5zaWdodHMgfHwgYm9vay5zdW1tYXJ5IH19IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYnNDb250ZW50PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJhcHBsaWNhdGlvbnNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtmb250U2l6ZUNsYXNzZXNbZm9udFNpemVdfSBzcGFjZS15LTRgfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBkYW5nZXJvdXNseVNldElubmVySFRNTD17eyBfX2h0bWw6IGJvb2suYXBwbGljYXRpb25zIHx8IFwiXCIgfX0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJzPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICB7c2hvd05vdGVzICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy02NCBib3JkZXItbCBwbC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxOb3Rlc1BhbmVsIGNvbnRlbnRJZD17Ym9vay5pZH0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAge2lzSGlnaGxpZ2h0aW5nICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgYm9yZGVyLXQgcHQtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPEhpZ2hsaWdodENvbnRyb2xzIGNvbnRlbnRJZD17Ym9vay5pZH0gLz5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICAgIHshaXNUdHNQbGF5aW5nICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgYm9yZGVyLXQgcHQtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRleHRUb1NwZWVjaFBsYXllclxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0ZXh0PXtib29rLnN1bW1hcnkucmVwbGFjZSgvPFtePl0qPi9nLCBcIlwiKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25QbGF5aW5nQ2hhbmdlPXtoYW5kbGVUdHNQbGF5aW5nQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDwvQ2FyZD5cclxuICAgICAgICAgICAgICA8L1RhYnNDb250ZW50PlxyXG5cclxuICAgICAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJ0b29sc1wiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cclxuICAgICAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwicC02IHJvdW5kZWQteGwgYm9yZGVyLW5vbmUgc2hhZG93LXNtIGNhcmQtdGhlbWVkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIG1iLTRcIj5GbGFzaGNhcmRzPC9oMj5cclxuICAgICAgICAgICAgICAgICAgICA8Rmxhc2hjYXJkQ3JlYXRvciBjb250ZW50SWQ9e2Jvb2suaWR9IGNvbnRlbnRUaXRsZT17Ym9vay50aXRsZX0gLz5cclxuICAgICAgICAgICAgICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwicC02IHJvdW5kZWQteGwgYm9yZGVyLW5vbmUgc2hhZG93LXNtIGNhcmQtdGhlbWVkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIG1iLTRcIj5UZXh0LXRvLVNwZWVjaDwvaDI+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRleHRUb1NwZWVjaFBsYXllclxyXG4gICAgICAgICAgICAgICAgICAgICAgdGV4dD17Ym9vay5zdW1tYXJ5LnJlcGxhY2UoLzxbXj5dKj4vZywgXCJcIil9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvblBsYXlpbmdDaGFuZ2U9e2hhbmRsZVR0c1BsYXlpbmdDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9UYWJzQ29udGVudD5cclxuXHJcbiAgICAgICAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwicXVpelwiPlxyXG4gICAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwicC02IHJvdW5kZWQteGwgYm9yZGVyLW5vbmUgc2hhZG93LXNtIGNhcmQtdGhlbWVkXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCBtYi00XCI+VGVzdCBZb3VyIFVuZGVyc3RhbmRpbmc8L2gyPlxyXG4gICAgICAgICAgICAgICAgICA8Q29tcHJlaGVuc2lvblF1aXpcclxuICAgICAgICAgICAgICAgICAgICBjb250ZW50SWQ9e2Jvb2suaWR9XHJcbiAgICAgICAgICAgICAgICAgICAgY29udGVudFRpdGxlPXtib29rLnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnRlbnRUeXBlPVwiYm9va1wiXHJcbiAgICAgICAgICAgICAgICAgICAgb25RdWl6QXR0ZW1wdD17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgLy8gVHJhY2sgcXVpeiBhdHRlbXB0IGFjdGl2aXR5XHJcbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCBtb2NrVXNlcklkID0gXCJ1c2VyLTEyM1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICB0cmFja1VzZXJBY3Rpdml0eShtb2NrVXNlcklkLCBcInF1aXpfYXR0ZW1wdFwiLCBib29rLmlkLCBcImJvb2tcIiwge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogYm9vay50aXRsZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2F0ZWdvcnk6IGJvb2suY2F0ZWdvcmllcz8uWzBdIHx8IFwiVW5jYXRlZ29yaXplZFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XHJcbiAgICAgICAgICAgIDwvVGFicz5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG5cclxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUmVmIiwiTGluayIsInVzZVJvdXRlciIsIkJ1dHRvbiIsIkNhcmQiLCJUYWJzIiwiVGFic0NvbnRlbnQiLCJUYWJzTGlzdCIsIlRhYnNUcmlnZ2VyIiwiQ2hldnJvbkxlZnQiLCJXaWZpIiwiV2lmaU9mZiIsIkxvYWRlcjIiLCJCb29rT3BlbiIsIkxpZ2h0YnVsYiIsIlNwYXJrbGVzIiwiTWVzc2FnZVNxdWFyZSIsIkJvb2tCb29rbWFyayIsIk9mZmxpbmVCb29rVG9nZ2xlIiwiZ2V0T2ZmbGluZUJvb2siLCJzYXZlUmVhZGluZ1Byb2dyZXNzIiwiZ2V0UmVhZGluZ1Byb2dyZXNzIiwiaXNCb29rQXZhaWxhYmxlT2ZmbGluZSIsInVzZVRvYXN0IiwiUmVhZGluZ1Byb2dyZXNzQmFyIiwiSGlnaGxpZ2h0Q29udHJvbHMiLCJOb3Rlc1BhbmVsIiwiVGV4dFRvU3BlZWNoUGxheWVyIiwiRmxhc2hjYXJkQ3JlYXRvciIsIlJlYWRpbmdTcGVlZFRyYWNrZXIiLCJTb2NpYWxTaGFyaW5nIiwiQ29tcHJlaGVuc2lvblF1aXoiLCJTaW1pbGFyQ29udGVudCIsInRyYWNrVXNlckFjdGl2aXR5IiwiYXBpIiwiQm9va1BhZ2UiLCJwYXJhbXMiLCJyb3V0ZXIiLCJ0b2FzdCIsImNvbnRlbnRSZWYiLCJib29rIiwic2V0Qm9vayIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJmb250U2l6ZSIsInNldEZvbnRTaXplIiwiaXNPZmZsaW5lIiwic2V0SXNPZmZsaW5lIiwiaXNPZmZsaW5lQXZhaWxhYmxlIiwic2V0SXNPZmZsaW5lQXZhaWxhYmxlIiwicmVhZGluZ1Byb2dyZXNzIiwic2V0UmVhZGluZ1Byb2dyZXNzIiwic2hvd05vdGVzIiwic2V0U2hvd05vdGVzIiwiaXNIaWdobGlnaHRpbmciLCJzZXRJc0hpZ2hsaWdodGluZyIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsInNlbGVjdGVkVGV4dEZvclNoYXJpbmciLCJzZXRTZWxlY3RlZFRleHRGb3JTaGFyaW5nIiwiaXNUdHNQbGF5aW5nIiwic2V0SXNUdHNQbGF5aW5nIiwicmVhZGluZ1N0YXJ0VGltZSIsInNldFJlYWRpbmdTdGFydFRpbWUiLCJyZWFkaW5nU2Vzc2lvbklkIiwic2V0UmVhZGluZ1Nlc3Npb25JZCIsIm5hdmlnYXRvciIsIm9uTGluZSIsImhhbmRsZU9ubGluZSIsImhhbmRsZU9mZmxpbmUiLCJ3aW5kb3ciLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImZldGNoQm9vayIsImJvb2tEYXRhIiwib2ZmbGluZUF2YWlsYWJsZSIsInNsdWciLCJnZXRCb29rQnlJZCIsInByb2dyZXNzIiwibW9ja1VzZXJJZCIsImlkIiwidGl0bGUiLCJjYXRlZ29yeSIsImNhdGVnb3JpZXMiLCJEYXRlIiwibm93IiwiZXJyIiwiY29uc29sZSIsImVuZFRpbWUiLCJ0aW1lU3BlbnRNaW51dGVzIiwiTWF0aCIsInJvdW5kIiwiZ2V0VGltZSIsInRpbWVTcGVudCIsImNvbXBsZXRpb25QZXJjZW50YWdlIiwic2Vzc2lvbklkIiwiY3VycmVudCIsImNvbnRlbnQiLCJ0b3RhbExlbmd0aCIsInNjcm9sbEhlaWdodCIsInNhdmVQcm9ncmVzcyIsInBvc2l0aW9uIiwic2Nyb2xsVG9wIiwibWluIiwiY2xpZW50SGVpZ2h0IiwibGFzdFJlYWRBdCIsInRvSVNPU3RyaW5nIiwiaXNOYU4iLCJ0aW1lb3V0IiwiaGFuZGxlU2Nyb2xsIiwiY2xlYXJUaW1lb3V0Iiwic2V0VGltZW91dCIsImhhbmRsZUJlZm9yZVVubG9hZCIsImhhbmRsZVNlbGVjdGlvbiIsInNlbGVjdGlvbiIsImdldFNlbGVjdGlvbiIsImlzQ29sbGFwc2VkIiwidG9TdHJpbmciLCJkb2N1bWVudCIsImZvbnRTaXplQ2xhc3NlcyIsInNtYWxsIiwibWVkaXVtIiwibGFyZ2UiLCJoYW5kbGVPZmZsaW5lU3RhdHVzQ2hhbmdlIiwiYXZhaWxhYmxlIiwiaGFuZGxlVHRzUGxheWluZ0NoYW5nZSIsInBsYXlpbmciLCJnZXRXb3JkQ291bnQiLCJ0ZXh0Iiwic3VtbWFyeSIsInJlcGxhY2UiLCJzcGxpdCIsImxlbmd0aCIsImRpdiIsImNsYXNzTmFtZSIsImhyZWYiLCJzcGFuIiwicCIsInZhcmlhbnQiLCJvbkNsaWNrIiwicHVzaCIsImltZyIsInNyYyIsImNvdmVyVXJsIiwiYWx0IiwiaDEiLCJhdXRob3IiLCJyYXRpbmciLCJzdmciLCJ4bWxucyIsIndpZHRoIiwiaGVpZ2h0Iiwidmlld0JveCIsImZpbGwiLCJzdHJva2UiLCJwb2x5Z29uIiwicG9pbnRzIiwicGFnZXMiLCJsYW5ndWFnZSIsImJvb2tJZCIsImJvb2tUaXRsZSIsIm9uQm9va21hcmsiLCJvblN0YXR1c0NoYW5nZSIsImNvbnRlbnRJZCIsImNvbnRlbnRUaXRsZSIsImNvbnRlbnRUeXBlIiwicXVvdGUiLCJ1bmRlZmluZWQiLCJjb250ZW50TGVuZ3RoIiwiZGVmYXVsdFZhbHVlIiwidmFsdWUiLCJvblZhbHVlQ2hhbmdlIiwic2l6ZSIsInN0cm9rZVdpZHRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwicGF0aCIsImQiLCJyZWYiLCJkYW5nZXJvdXNseVNldElubmVySFRNTCIsIl9faHRtbCIsImtleUluc2lnaHRzIiwiYXBwbGljYXRpb25zIiwib25QbGF5aW5nQ2hhbmdlIiwiaDIiLCJvblF1aXpBdHRlbXB0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/books/[slug]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api-service.ts":
/*!****************************!*\
  !*** ./lib/api-service.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiService: () => (/* binding */ ApiService)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Configuration for Django API\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';\n// API service class for handling external Django API calls\nclass ApiService {\n    // Generic fetch wrapper with error handling\n    static async fetchWithErrorHandling(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseUrl).concat(endpoint);\n        try {\n            const response = await fetch(url, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    ...options.headers\n                },\n                ...options\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            return await response.json();\n        } catch (error) {\n            console.error(\"API call failed for \".concat(endpoint, \":\"), error);\n            throw error;\n        }\n    }\n    // Books API methods\n    static async getBooks(params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.category) searchParams.append('category', params.category);\n        if (params === null || params === void 0 ? void 0 : params.query) searchParams.append('query', params.query);\n        if ((params === null || params === void 0 ? void 0 : params.premium) !== undefined) searchParams.append('premium', params.premium.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        const endpoint = \"/books/\".concat(searchParams.toString() ? \"?\".concat(searchParams.toString()) : '');\n        return this.fetchWithErrorHandling(endpoint);\n    }\n    static async getBookById(id) {\n        return this.fetchWithErrorHandling(\"/books/\".concat(id, \"/\"));\n    }\n    // Business Plans API methods\n    static async getBusinessPlans(params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.size) searchParams.append('size', params.size);\n        if (params === null || params === void 0 ? void 0 : params.category) searchParams.append('category', params.category);\n        if (params === null || params === void 0 ? void 0 : params.query) searchParams.append('query', params.query);\n        if ((params === null || params === void 0 ? void 0 : params.premium) !== undefined) searchParams.append('premium', params.premium.toString());\n        const endpoint = \"/business-plans/\".concat(searchParams.toString() ? \"?\".concat(searchParams.toString()) : '');\n        return this.fetchWithErrorHandling(endpoint);\n    }\n    static async getBusinessPlanById(id) {\n        return this.fetchWithErrorHandling(\"/business-plans/\".concat(id, \"/\"));\n    }\n    // Authentication API methods\n    static async login(credentials) {\n        return this.fetchWithErrorHandling('/auth/login/', {\n            method: 'POST',\n            body: JSON.stringify(credentials)\n        });\n    }\n    static async register(userData) {\n        return this.fetchWithErrorHandling('/auth/register/', {\n            method: 'POST',\n            body: JSON.stringify(userData)\n        });\n    }\n    // User API methods\n    static async getUserProfile(userId) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/\"));\n    }\n    static async updateUserProfile(userId, userData) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/\"), {\n            method: 'PATCH',\n            body: JSON.stringify(userData)\n        });\n    }\n    // Payment API methods\n    static async processPayment(paymentData) {\n        return this.fetchWithErrorHandling('/payments/', {\n            method: 'POST',\n            body: JSON.stringify(paymentData)\n        });\n    }\n    static async verifyPayment(transactionId) {\n        return this.fetchWithErrorHandling(\"/payments/verify/\".concat(transactionId, \"/\"));\n    }\n    static async getPaymentHistory(userId) {\n        return this.fetchWithErrorHandling(\"/payments/?userId=\".concat(userId));\n    }\n    // Admin API methods (for admin dashboard)\n    static async getAdminStats() {\n        return this.fetchWithErrorHandling('/admin/stats/');\n    }\n    static async getAdminUsers(params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        const endpoint = \"/admin/users/\".concat(searchParams.toString() ? \"?\".concat(searchParams.toString()) : '');\n        return this.fetchWithErrorHandling(endpoint);\n    }\n    // Bookmarks API methods\n    static async getUserBookmarks(userId) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/bookmarks/\"));\n    }\n    static async addBookmark(userId, bookId) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/bookmarks/\"), {\n            method: 'POST',\n            body: JSON.stringify({\n                bookId\n            })\n        });\n    }\n    static async removeBookmark(userId, bookId) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/bookmarks/\").concat(bookId, \"/\"), {\n            method: 'DELETE'\n        });\n    }\n    // Analytics API methods\n    static async getUserAnalytics(userId) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/analytics/\"));\n    }\n}\nApiService.baseUrl = API_BASE_URL;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api-service.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: () => (/* binding */ API_CONFIG),\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   authTokenManager: () => (/* binding */ authTokenManager),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   isUsingMockApi: () => (/* binding */ isUsingMockApi),\n/* harmony export */   useApiCall: () => (/* binding */ useApiCall),\n/* harmony export */   userSessionManager: () => (/* binding */ userSessionManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _api_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api-service */ \"(app-pages-browser)/./lib/api-service.ts\");\n/* harmony import */ var _mock_api_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mock-api-service */ \"(app-pages-browser)/./lib/mock-api-service.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n\n\n// Environment configuration\nconst USE_MOCK_API = process.env.NEXT_PUBLIC_USE_MOCK_API === 'true' || \"development\" === 'development';\n// Export the appropriate service based on environment\nconst api = USE_MOCK_API ? _mock_api_service__WEBPACK_IMPORTED_MODULE_2__.MockApiService : _api_service__WEBPACK_IMPORTED_MODULE_1__.ApiService;\n// Utility function to check if we're using mock API\nconst isUsingMockApi = ()=>USE_MOCK_API;\n// Helper function for error handling in components\nconst handleApiError = (error)=>{\n    if (error instanceof Error) {\n        return error.message;\n    }\n    return 'An unexpected error occurred';\n};\n// Configuration constants\nconst API_CONFIG = {\n    BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api',\n    TIMEOUT: 10000,\n    RETRY_ATTEMPTS: 3,\n    USE_MOCK: USE_MOCK_API\n};\n// Authentication token management\nconst authTokenManager = {\n    getToken: ()=>{\n        if (false) {}\n        return localStorage.getItem('auth_token');\n    },\n    setToken: (token)=>{\n        if (false) {}\n        localStorage.setItem('auth_token', token);\n    },\n    removeToken: ()=>{\n        if (false) {}\n        localStorage.removeItem('auth_token');\n    },\n    isAuthenticated: ()=>{\n        return !!authTokenManager.getToken();\n    }\n};\n// User session management\nconst userSessionManager = {\n    getCurrentUser: ()=>{\n        if (false) {}\n        const userStr = localStorage.getItem('current_user');\n        return userStr ? JSON.parse(userStr) : null;\n    },\n    setCurrentUser: (user)=>{\n        if (false) {}\n        localStorage.setItem('current_user', JSON.stringify(user));\n    },\n    removeCurrentUser: ()=>{\n        if (false) {}\n        localStorage.removeItem('current_user');\n    },\n    logout: ()=>{\n        authTokenManager.removeToken();\n        userSessionManager.removeCurrentUser();\n    }\n};\n// Custom hook for API calls with loading and error states\nconst useApiCall = function(apiCall) {\n    let dependencies = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await apiCall();\n            setData(result);\n        } catch (err) {\n            setError(handleApiError(err));\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useApiCall.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"useApiCall.useEffect\"], dependencies);\n    return {\n        data,\n        loading,\n        error,\n        refetch: fetchData\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/mock-api-service.ts":
/*!*********************************!*\
  !*** ./lib/mock-api-service.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MockApiService: () => (/* binding */ MockApiService)\n/* harmony export */ });\n/* harmony import */ var _mock_data__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mock-data */ \"(app-pages-browser)/./lib/mock-data.ts\");\n\n// Mock API service for development when Django backend is not available\nclass MockApiService {\n    // Simulate network delay\n    static delay() {\n        let ms = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 500;\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    // Books API methods\n    static async getBooks(params) {\n        await this.delay();\n        let filteredBooks = [\n            ..._mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBookPreviews\n        ];\n        if ((params === null || params === void 0 ? void 0 : params.category) && params.category !== \"all\") {\n            filteredBooks = filteredBooks.filter((book)=>book.category.toLowerCase() === params.category.toLowerCase());\n        }\n        if (params === null || params === void 0 ? void 0 : params.query) {\n            const searchQuery = params.query.toLowerCase();\n            filteredBooks = filteredBooks.filter((book)=>book.title.toLowerCase().includes(searchQuery) || book.author.toLowerCase().includes(searchQuery));\n        }\n        if ((params === null || params === void 0 ? void 0 : params.premium) !== undefined) {\n            const fullBooks = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBooks.filter((book)=>book.isPremium === params.premium);\n            const premiumBookIds = fullBooks.map((book)=>book.id);\n            filteredBooks = filteredBooks.filter((book)=>premiumBookIds.includes(book.id));\n        }\n        if (params === null || params === void 0 ? void 0 : params.limit) {\n            filteredBooks = filteredBooks.slice(0, params.limit);\n        }\n        return filteredBooks;\n    }\n    static async getBookById(id) {\n        await this.delay();\n        const book = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBooks.find((book)=>book.id === id);\n        if (!book) {\n            throw new Error(\"Book with id \".concat(id, \" not found\"));\n        }\n        return book;\n    }\n    // Business Plans API methods\n    static async getBusinessPlans(params) {\n        await this.delay();\n        let filteredPlans = [\n            ..._mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBusinessPlanPreviews\n        ];\n        if ((params === null || params === void 0 ? void 0 : params.size) && params.size !== \"all\") {\n            filteredPlans = filteredPlans.filter((plan)=>plan.size === params.size);\n        }\n        if ((params === null || params === void 0 ? void 0 : params.category) && params.category !== \"all\") {\n            filteredPlans = filteredPlans.filter((plan)=>plan.category.toLowerCase() === params.category.toLowerCase());\n        }\n        if (params === null || params === void 0 ? void 0 : params.query) {\n            const searchQuery = params.query.toLowerCase();\n            filteredPlans = filteredPlans.filter((plan)=>plan.title.toLowerCase().includes(searchQuery) || plan.category.toLowerCase().includes(searchQuery));\n        }\n        if ((params === null || params === void 0 ? void 0 : params.premium) !== undefined) {\n            filteredPlans = filteredPlans.filter((plan)=>plan.isPremium === params.premium);\n        }\n        return filteredPlans;\n    }\n    static async getBusinessPlanById(id) {\n        await this.delay();\n        const plan = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBusinessPlans.find((plan)=>plan.id === id);\n        if (!plan) {\n            throw new Error(\"Business plan with id \".concat(id, \" not found\"));\n        }\n        return plan;\n    }\n    // Authentication API methods\n    static async login(credentials) {\n        await this.delay();\n        // Mock successful login for demo purposes\n        return {\n            id: \"user-1\",\n            firstName: \"John\",\n            lastName: \"Doe\",\n            email: credentials.email,\n            plan: \"medium\",\n            token: \"mock-jwt-token-123\"\n        };\n    }\n    static async register(userData) {\n        await this.delay();\n        // Mock successful registration\n        return {\n            id: \"user-\".concat(Date.now()),\n            firstName: userData.firstName,\n            lastName: userData.lastName,\n            email: userData.email,\n            plan: \"base\",\n            token: \"mock-jwt-token-456\"\n        };\n    }\n    // User API methods\n    static async getUserProfile(userId) {\n        await this.delay();\n        return {\n            user: {\n                id: userId,\n                firstName: \"John\",\n                lastName: \"Doe\",\n                email: \"<EMAIL>\",\n                plan: \"medium\"\n            },\n            activities: [\n                {\n                    id: \"activity-1\",\n                    userId: userId,\n                    type: \"book_read\",\n                    itemId: \"the-psychology-of-money\",\n                    itemTitle: \"The Psychology of Money\",\n                    timestamp: new Date(Date.now() - 86400000).toISOString()\n                },\n                {\n                    id: \"activity-2\",\n                    userId: userId,\n                    type: \"plan_viewed\",\n                    itemId: \"small-1\",\n                    itemTitle: \"Local Coffee Shop\",\n                    timestamp: new Date(Date.now() - 172800000).toISOString()\n                }\n            ]\n        };\n    }\n    static async updateUserProfile(userId, userData) {\n        await this.delay();\n        return {\n            id: userId,\n            firstName: userData.firstName || \"John\",\n            lastName: userData.lastName || \"Doe\",\n            email: userData.email || \"<EMAIL>\",\n            plan: \"medium\"\n        };\n    }\n    // Payment API methods\n    static async processPayment(paymentData) {\n        await this.delay(1000) // Longer delay for payment processing\n        ;\n        // Mock successful payment\n        return {\n            success: true,\n            transactionId: \"TRX-\".concat(Date.now(), \"-\").concat(Math.floor(Math.random() * 1000)),\n            message: \"Payment processed successfully\",\n            timestamp: new Date().toISOString(),\n            plan: paymentData.plan\n        };\n    }\n    static async verifyPayment(transactionId) {\n        await this.delay();\n        return {\n            success: true,\n            status: \"completed\",\n            message: \"Payment verified successfully\"\n        };\n    }\n    static async getPaymentHistory(userId) {\n        await this.delay();\n        return [\n            {\n                success: true,\n                transactionId: \"TRX-123456789\",\n                message: \"Payment processed successfully\",\n                timestamp: new Date(Date.now() - 2592000000).toISOString(),\n                plan: \"medium\"\n            }\n        ];\n    }\n    // Admin API methods\n    static async getAdminStats() {\n        await this.delay();\n        return {\n            totalUsers: 1250,\n            totalBooks: _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBooks.length,\n            totalBusinessPlans: _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBusinessPlans.length,\n            revenueThisMonth: 15750\n        };\n    }\n    static async getAdminUsers(params) {\n        await this.delay();\n        const mockUsers = [\n            {\n                id: \"user-1\",\n                firstName: \"John\",\n                lastName: \"Doe\",\n                email: \"<EMAIL>\",\n                plan: \"medium\",\n                createdAt: \"2023-01-15T00:00:00Z\"\n            },\n            {\n                id: \"user-2\",\n                firstName: \"Jane\",\n                lastName: \"Smith\",\n                email: \"<EMAIL>\",\n                plan: \"base\",\n                createdAt: \"2023-03-22T00:00:00Z\"\n            }\n        ];\n        return {\n            users: mockUsers,\n            total: mockUsers.length,\n            page: (params === null || params === void 0 ? void 0 : params.page) || 1,\n            totalPages: 1\n        };\n    }\n    // Bookmarks API methods\n    static async getUserBookmarks(userId) {\n        await this.delay();\n        // Return first 2 books as bookmarked for demo\n        return _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBookPreviews.slice(0, 2);\n    }\n    static async addBookmark(userId, bookId) {\n        await this.delay();\n        return {\n            success: true\n        };\n    }\n    static async removeBookmark(userId, bookId) {\n        await this.delay();\n        return {\n            success: true\n        };\n    }\n    // Analytics API methods\n    static async getUserAnalytics(userId) {\n        await this.delay();\n        return {\n            readingStats: {\n                booksRead: 12,\n                totalReadingTime: 2400,\n                averageReadingSpeed: 250,\n                streakDays: 7\n            },\n            recentActivity: [\n                {\n                    id: \"activity-1\",\n                    type: \"book_read\",\n                    itemTitle: \"The Psychology of Money\",\n                    timestamp: new Date(Date.now() - 86400000).toISOString()\n                },\n                {\n                    id: \"activity-2\",\n                    type: \"plan_viewed\",\n                    itemTitle: \"Local Coffee Shop\",\n                    timestamp: new Date(Date.now() - 172800000).toISOString()\n                }\n            ]\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/mock-api-service.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/mock-data.ts":
/*!**************************!*\
  !*** ./lib/mock-data.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mockBookPreviews: () => (/* binding */ mockBookPreviews),\n/* harmony export */   mockBooks: () => (/* binding */ mockBooks),\n/* harmony export */   mockBusinessPlanPreviews: () => (/* binding */ mockBusinessPlanPreviews),\n/* harmony export */   mockBusinessPlans: () => (/* binding */ mockBusinessPlans)\n/* harmony export */ });\n// Mock data for books - this will be replaced by Django API calls\nconst mockBooks = [\n    {\n        id: \"the-psychology-of-money\",\n        title: \"The Psychology Of Money\",\n        author: \"Morgan Housel\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Finance\",\n        rating: 4.4,\n        pages: 242,\n        language: \"English\",\n        summary: \"\\n      <p>The Psychology of Money explores how money moves around in an economy and how people behave with it. The author, Morgan Housel, provides timeless lessons on wealth, greed, and happiness.</p>\\n      \\n      <h3>Key Insights:</h3>\\n      \\n      <p><strong>1. Financial success is not a hard science</strong></p>\\n      <p>It's a soft skill where how you behave is more important than what you know. Financial outcomes are driven by luck, independent of intelligence and effort.</p>\\n      \\n      <p><strong>2. No one is crazy with money</strong></p>\\n      <p>People make financial decisions based on their unique experiences, their own personal history, unique worldview, ego, pride, marketing, and odd incentives.</p>\\n      \\n      <p><strong>3. Luck and risk are siblings</strong></p>\\n      <p>They are both the reality that every outcome in life is guided by forces other than individual effort. They both happen because the world is too complex to allow 100% of your actions to dictate 100% of your outcomes.</p>\\n      \\n      <p><strong>4. Never enough</strong></p>\\n      <p>When rich people do crazy things, it's often a case of trying to feel valued after already having more money than they know what to do with.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Financial success is not a hard science</strong></p>\\n      <p>It's a soft skill where how you behave is more important than what you know. Financial outcomes are driven by luck, independent of intelligence and effort.</p>\\n      \\n      <p><strong>2. No one is crazy with money</strong></p>\\n      <p>People make financial decisions based on their unique experiences, their own personal history, unique worldview, ego, pride, marketing, and odd incentives.</p>\\n      \\n      <p><strong>3. Luck and risk are siblings</strong></p>\\n      <p>They are both the reality that every outcome in life is guided by forces other than individual effort. They both happen because the world is too complex to allow 100% of your actions to dictate 100% of your outcomes.</p>\\n      \\n      <p><strong>4. Never enough</strong></p>\\n      <p>When rich people do crazy things, it's often a case of trying to feel valued after already having more money than they know what to do with.</p>\\n    \",\n        applications: \"\\n      <p><strong>For Personal Finance:</strong></p>\\n      <ul>\\n        <li>Save money without a specific goal in mind</li>\\n        <li>Gain control over your time</li>\\n        <li>Be reasonable rather than rational</li>\\n        <li>Aim for enough, not for maximum</li>\\n      </ul>\\n      \\n      <p><strong>For Investors:</strong></p>\\n      <ul>\\n        <li>Understand the role of luck and risk</li>\\n        <li>Know that getting wealthy and staying wealthy are different skills</li>\\n        <li>Long tails drive everything - a small number of events can account for the majority of outcomes</li>\\n        <li>Use room for error when investing - prepare for a range of outcomes</li>\\n      </ul>\\n    \",\n        isPremium: false\n    },\n    {\n        id: \"atomic-habits\",\n        title: \"Atomic Habits\",\n        author: \"James Clear\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Productivity\",\n        rating: 4.9,\n        pages: 320,\n        language: \"English\",\n        summary: \"\\n      <p>Atomic Habits offers a proven framework for improving every day. James Clear reveals practical strategies that will teach you exactly how to form good habits, break bad ones, and master the tiny behaviors that lead to remarkable results.</p>\\n      \\n      <h3>Key Insights:</h3>\\n      \\n      <p><strong>1. Habits are the compound interest of self-improvement</strong></p>\\n      <p>Small changes often appear to make no difference until you cross a critical threshold. The effects of your habits multiply as you repeat them.</p>\\n      \\n      <p><strong>2. Focus on systems instead of goals</strong></p>\\n      <p>Goals are about the results you want to achieve. Systems are about the processes that lead to those results. Focus on the system, not the goal.</p>\\n      \\n      <p><strong>3. The Four Laws of Behavior Change</strong></p>\\n      <p>Make it obvious, make it attractive, make it easy, and make it satisfying. These are the fundamental principles behind habit formation.</p>\\n      \\n      <p><strong>4. Identity-based habits</strong></p>\\n      <p>The most effective way to change your habits is to focus not on what you want to achieve, but on who you wish to become.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Habits are the compound interest of self-improvement</strong></p>\\n      <p>Small changes often appear to make no difference until you cross a critical threshold. The effects of your habits multiply as you repeat them.</p>\\n      \\n      <p><strong>2. Focus on systems instead of goals</strong></p>\\n      <p>Goals are about the results you want to achieve. Systems are about the processes that lead to those results. Focus on the system, not the goal.</p>\\n      \\n      <p><strong>3. The Four Laws of Behavior Change</strong></p>\\n      <p>Make it obvious, make it attractive, make it easy, and make it satisfying. These are the fundamental principles behind habit formation.</p>\\n      \\n      <p><strong>4. Identity-based habits</strong></p>\\n      <p>The most effective way to change your habits is to focus not on what you want to achieve, but on who you wish to become.</p>\\n    \",\n        applications: \"\\n      <p><strong>For Personal Development:</strong></p>\\n      <ul>\\n        <li>Start with an incredibly small habit</li>\\n        <li>Increase your habit in very small ways</li>\\n        <li>Break habits into chunks</li>\\n        <li>When you slip, get back on track quickly</li>\\n      </ul>\\n      \\n      <p><strong>For Business:</strong></p>\\n      <ul>\\n        <li>Create an environment where doing the right thing is as easy as possible</li>\\n        <li>Make good habits obvious in your environment</li>\\n        <li>Reduce friction for good habits</li>\\n        <li>Increase friction for bad habits</li>\\n      </ul>\\n    \",\n        isPremium: false\n    },\n    {\n        id: \"sapiens\",\n        title: \"Sapiens\",\n        author: \"Yuval Noah Harari\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"History\",\n        rating: 4.7,\n        pages: 464,\n        language: \"English\",\n        summary: '\\n      <p>Sapiens: A Brief History of Humankind is a book by Yuval Noah Harari that explores the history and impact of Homo sapiens on the world. It traces the evolution of our species from the emergence of Homo sapiens in Africa to our current status as the dominant force on Earth.</p>\\n      \\n      <h3>Key Insights:</h3>\\n      \\n      <p><strong>1. The Cognitive Revolution</strong></p>\\n      <p>Around 70,000 years ago, Homo sapiens developed unique cognitive abilities, particularly the capacity for fiction and imagination, which allowed for unprecedented cooperation among large groups.</p>\\n      \\n      <p><strong>2. The Agricultural Revolution</strong></p>\\n      <p>Beginning about 12,000 years ago, humans transitioned from hunting and gathering to farming, which Harari describes as \"history\\'s biggest fraud\" because it led to harder work, less leisure, and poorer health for most people.</p>\\n      \\n      <p><strong>3. The Unification of Humankind</strong></p>\\n      <p>Over time, humans have created increasingly larger networks of cooperation through shared myths, including money, empires, and religions.</p>\\n      \\n      <p><strong>4. The Scientific Revolution</strong></p>\\n      <p>The last 500 years have seen an explosion of human power through the willingness to admit ignorance and the development of the scientific method.</p>\\n    ',\n        keyInsights: '\\n      <p><strong>1. The Cognitive Revolution</strong></p>\\n      <p>Around 70,000 years ago, Homo sapiens developed unique cognitive abilities, particularly the capacity for fiction and imagination, which allowed for unprecedented cooperation among large groups.</p>\\n      \\n      <p><strong>2. The Agricultural Revolution</strong></p>\\n      <p>Beginning about 12,000 years ago, humans transitioned from hunting and gathering to farming, which Harari describes as \"history\\'s biggest fraud\" because it led to harder work, less leisure, and poorer health for most people.</p>\\n      \\n      <p><strong>3. The Unification of Humankind</strong></p>\\n      <p>Over time, humans have created increasingly larger networks of cooperation through shared myths, including money, empires, and religions.</p>\\n      \\n      <p><strong>4. The Scientific Revolution</strong></p>\\n      <p>The last 500 years have seen an explosion of human power through the willingness to admit ignorance and the development of the scientific method.</p>\\n    ',\n        applications: '\\n      <p><strong>For Understanding Society:</strong></p>\\n      <ul>\\n        <li>Recognize how shared myths and stories shape our world</li>\\n        <li>Understand the historical context of current social structures</li>\\n        <li>Question whether \"progress\" always means improvement</li>\\n        <li>Consider the ethical implications of technological advancement</li>\\n      </ul>\\n      \\n      <p><strong>For Business and Leadership:</strong></p>\\n      <ul>\\n        <li>Appreciate how shared narratives create cohesion in organizations</li>\\n        <li>Understand how money and corporations are social constructs that depend on trust</li>\\n        <li>Consider the long-term implications of short-term decisions</li>\\n        <li>Recognize patterns of human behavior that persist across time</li>\\n      </ul>\\n    ',\n        isPremium: true\n    },\n    {\n        id: \"zero-to-one\",\n        title: \"Zero to One\",\n        author: \"Peter Thiel\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Entrepreneurship\",\n        rating: 4.8,\n        pages: 224,\n        language: \"English\",\n        summary: \"\\n      <p>Zero to One presents at once an optimistic view of the future of progress in America and a new way of thinking about innovation: it starts by learning to ask the questions that lead you to find value in unexpected places.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Vertical vs. Horizontal Progress</strong></p>\\n      <p>Horizontal progress means copying things that work—going from 1 to n. Vertical progress means doing new things—going from 0 to 1. The focus should be on creating something new rather than copying existing models.</p>\\n\\n      <p><strong>2. Monopolies vs. Competition</strong></p>\\n      <p>Competition is for losers. Under perfect competition, no company makes economic profit. The goal should be to create a monopoly through innovation.</p>\\n\\n      <p><strong>3. The Power Law</strong></p>\\n      <p>A small number of companies radically outperform all others. This principle applies to venture capital investments, where a single investment may return more than all others combined.</p>\\n\\n      <p><strong>4. Secrets</strong></p>\\n      <p>Every great business is built around a secret that's hidden from the outside. Great companies find value in unexpected places by thinking about business from first principles.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Vertical vs. Horizontal Progress</strong></p>\\n      <p>Horizontal progress means copying things that work—going from 1 to n. Vertical progress means doing new things—going from 0 to 1. The focus should be on creating something new rather than copying existing models.</p>\\n\\n      <p><strong>2. Monopolies vs. Competition</strong></p>\\n      <p>Competition is for losers. Under perfect competition, no company makes economic profit. The goal should be to create a monopoly through innovation.</p>\\n\\n      <p><strong>3. The Power Law</strong></p>\\n      <p>A small number of companies radically outperform all others. This principle applies to venture capital investments, where a single investment may return more than all others combined.</p>\\n\\n      <p><strong>4. Secrets</strong></p>\\n      <p>Every great business is built around a secret that's hidden from the outside. Great companies find value in unexpected places by thinking about business from first principles.</p>\\n    \",\n        applications: '\\n      <p><strong>For Entrepreneurs:</strong></p>\\n      <ul>\\n        <li>Focus on creating something new rather than improving existing products</li>\\n        <li>Aim to create a monopoly through unique technology, network effects, economies of scale, and branding</li>\\n        <li>Start small and monopolize a niche market before expanding</li>\\n        <li>Build a great team with a strong, unified vision</li>\\n      </ul>\\n\\n      <p><strong>For Investors:</strong></p>\\n      <ul>\\n        <li>Understand that returns follow a power law—a few investments will outperform all others</li>\\n        <li>Look for companies with proprietary technology, network effects, economies of scale, and strong branding</li>\\n        <li>Evaluate the founding team\\'s dynamics and vision</li>\\n        <li>Consider whether the company has discovered a unique \"secret\" about the market</li>\\n      </ul>\\n    ',\n        isPremium: true\n    },\n    {\n        id: \"good-to-great\",\n        title: \"Good to Great\",\n        author: \"Jim Collins\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Leadership\",\n        rating: 4.7,\n        pages: 320,\n        language: \"English\",\n        summary: \"\\n      <p>Good to Great presents the findings of a five-year study by Jim Collins and his research team. The team identified a set of companies that made the leap from good results to great results and sustained those results for at least fifteen years.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Level 5 Leadership</strong></p>\\n      <p>Leaders who have a unique blend of personal humility and professional will. They are ambitious for the company, not themselves.</p>\\n\\n      <p><strong>2. First Who, Then What</strong></p>\\n      <p>Get the right people on the bus, the wrong people off the bus, and the right people in the right seats—then figure out where to drive it.</p>\\n\\n      <p><strong>3. Confront the Brutal Facts</strong></p>\\n      <p>Create a culture where people have the opportunity to be heard and where the truth is heard. Maintain unwavering faith that you can and will prevail, regardless of difficulties.</p>\\n\\n      <p><strong>4. The Hedgehog Concept</strong></p>\\n      <p>Focus on the intersection of three circles: what you can be the best in the world at, what drives your economic engine, and what you are deeply passionate about.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Level 5 Leadership</strong></p>\\n      <p>Leaders who have a unique blend of personal humility and professional will. They are ambitious for the company, not themselves.</p>\\n\\n      <p><strong>2. First Who, Then What</strong></p>\\n      <p>Get the right people on the bus, the wrong people off the bus, and the right people in the right seats—then figure out where to drive it.</p>\\n\\n      <p><strong>3. Confront the Brutal Facts</strong></p>\\n      <p>Create a culture where people have the opportunity to be heard and where the truth is heard. Maintain unwavering faith that you can and will prevail, regardless of difficulties.</p>\\n\\n      <p><strong>4. The Hedgehog Concept</strong></p>\\n      <p>Focus on the intersection of three circles: what you can be the best in the world at, what drives your economic engine, and what you are deeply passionate about.</p>\\n    \",\n        applications: \"\\n      <p><strong>For Business Leaders:</strong></p>\\n      <ul>\\n        <li>Develop Level 5 Leadership qualities: ambition for the company over self</li>\\n        <li>Focus on getting the right team in place before determining strategy</li>\\n        <li>Create a culture of disciplined people, thought, and action</li>\\n        <li>Apply the Hedgehog Concept to focus resources and efforts</li>\\n      </ul>\\n\\n      <p><strong>For Organizations:</strong></p>\\n      <ul>\\n        <li>Use technology as an accelerator, not a creator of momentum</li>\\n        <li>Build momentum gradually until breakthrough occurs (the flywheel effect)</li>\\n        <li>Maintain discipline to stick with what you can be best at</li>\\n        <li>Confront reality while maintaining faith in ultimate success</li>\\n      </ul>\\n    \",\n        isPremium: true\n    },\n    {\n        id: \"the-lean-startup\",\n        title: \"The Lean Startup\",\n        author: \"Eric Ries\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Startup\",\n        rating: 4.6,\n        pages: 336,\n        language: \"English\",\n        summary: \"\\n      <p>The Lean Startup introduces a methodology for developing businesses and products that aims to shorten product development cycles and rapidly discover if a proposed business model is viable.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Build-Measure-Learn</strong></p>\\n      <p>The fundamental activity of a startup is to turn ideas into products, measure how customers respond, and then learn whether to pivot or persevere.</p>\\n\\n      <p><strong>2. Minimum Viable Product (MVP)</strong></p>\\n      <p>The version of a new product that allows a team to collect the maximum amount of validated learning about customers with the least effort.</p>\\n\\n      <p><strong>3. Validated Learning</strong></p>\\n      <p>The process of demonstrating empirically that a team has discovered valuable truths about a startup's present and future business prospects.</p>\\n\\n      <p><strong>4. Innovation Accounting</strong></p>\\n      <p>A quantitative approach that allows startups to prove objectively that they are learning how to grow a sustainable business.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Build-Measure-Learn</strong></p>\\n      <p>The fundamental activity of a startup is to turn ideas into products, measure how customers respond, and then learn whether to pivot or persevere.</p>\\n\\n      <p><strong>2. Minimum Viable Product (MVP)</strong></p>\\n      <p>The version of a new product that allows a team to collect the maximum amount of validated learning about customers with the least effort.</p>\\n\\n      <p><strong>3. Validated Learning</strong></p>\\n      <p>The process of demonstrating empirically that a team has discovered valuable truths about a startup's present and future business prospects.</p>\\n\\n      <p><strong>4. Innovation Accounting</strong></p>\\n      <p>A quantitative approach that allows startups to prove objectively that they are learning how to grow a sustainable business.</p>\\n    \",\n        applications: \"\\n      <p><strong>For Entrepreneurs:</strong></p>\\n      <ul>\\n        <li>Start with a minimum viable product to test assumptions quickly</li>\\n        <li>Use actionable metrics that demonstrate clear cause and effect</li>\\n        <li>Practice continuous deployment and small batch sizes</li>\\n        <li>Be willing to pivot when necessary based on validated learning</li>\\n      </ul>\\n\\n      <p><strong>For Established Companies:</strong></p>\\n      <ul>\\n        <li>Create innovation teams with appropriate structures and metrics</li>\\n        <li>Allocate resources using innovation accounting</li>\\n        <li>Develop internal entrepreneurship through dedicated teams</li>\\n        <li>Apply lean principles to accelerate product development cycles</li>\\n      </ul>\\n    \",\n        isPremium: true\n    }\n];\n// Convert to BookPreview format for listing pages\nconst mockBookPreviews = mockBooks.map((book)=>({\n        id: book.id,\n        title: book.title,\n        author: book.author,\n        coverUrl: book.coverUrl,\n        category: book.category,\n        rating: book.rating\n    }));\nconst mockBusinessPlans = [\n    {\n        id: \"small-1\",\n        title: \"Local Coffee Shop\",\n        category: \"Food & Beverage\",\n        size: \"small\",\n        description: \"A comprehensive business plan for starting and operating a successful local coffee shop.\",\n        overview: \"\\n      <h3>Executive Summary</h3>\\n      <p>This business plan outlines the establishment of a cozy, community-focused coffee shop that serves premium coffee and light food items. The shop will be located in a high-traffic area with significant foot traffic and limited direct competition.</p>\\n\\n      <h3>Business Concept</h3>\\n      <ul>\\n        <li>Premium coffee and espresso drinks</li>\\n        <li>Fresh pastries and light meals</li>\\n        <li>Comfortable seating and free Wi-Fi</li>\\n        <li>Focus on sustainable practices</li>\\n      </ul>\\n\\n      <h3>Target Market</h3>\\n      <ul>\\n        <li>Young professionals (25-40)</li>\\n        <li>College students</li>\\n        <li>Remote workers</li>\\n        <li>Local residents</li>\\n      </ul>\\n    \",\n        marketAnalysis: \"\\n      <h3>Market Overview</h3>\\n      <p>The coffee shop industry continues to grow, with increasing demand for premium coffee experiences. Key market trends include:</p>\\n      <ul>\\n        <li>Growing preference for specialty coffee</li>\\n        <li>Increased focus on sustainability</li>\\n        <li>Rising demand for plant-based options</li>\\n        <li>Need for comfortable workspaces</li>\\n      </ul>\\n\\n      <h3>Competitive Analysis</h3>\\n      <p>Local competition includes:</p>\\n      <ul>\\n        <li>Chain coffee shops (2 within 1km)</li>\\n        <li>Independent cafes (1 within 1km)</li>\\n        <li>Restaurants serving coffee</li>\\n      </ul>\\n\\n      <h3>Competitive Advantage</h3>\\n      <ul>\\n        <li>Premium quality coffee</li>\\n        <li>Comfortable atmosphere</li>\\n        <li>Excellent customer service</li>\\n        <li>Strategic location</li>\\n      </ul>\\n    \",\n        financials: \"\\n      <h3>Startup Costs</h3>\\n      <ul>\\n        <li>Lease deposit and improvements: $25,000</li>\\n        <li>Equipment: $35,000</li>\\n        <li>Initial inventory: $5,000</li>\\n        <li>Licenses and permits: $2,000</li>\\n        <li>Working capital: $20,000</li>\\n      </ul>\\n\\n      <h3>Financial Projections</h3>\\n      <p>Year 1:</p>\\n      <ul>\\n        <li>Revenue: $300,000</li>\\n        <li>Expenses: $270,000</li>\\n        <li>Net profit: $30,000</li>\\n      </ul>\\n\\n      <p>Year 2:</p>\\n      <ul>\\n        <li>Revenue: $400,000</li>\\n        <li>Expenses: $340,000</li>\\n        <li>Net profit: $60,000</li>\\n      </ul>\\n    \",\n        implementation: \"\\n      <h3>Timeline</h3>\\n      <ul>\\n        <li>Month 1-2: Location selection and lease signing</li>\\n        <li>Month 2-3: Design and permits</li>\\n        <li>Month 3-4: Construction and equipment installation</li>\\n        <li>Month 4: Staff hiring and training</li>\\n        <li>Month 5: Soft opening and marketing</li>\\n        <li>Month 6: Grand opening</li>\\n      </ul>\\n\\n      <h3>Marketing Strategy</h3>\\n      <ul>\\n        <li>Social media presence</li>\\n        <li>Local partnerships</li>\\n        <li>Loyalty program</li>\\n        <li>Community events</li>\\n      </ul>\\n\\n      <h3>Risk Mitigation</h3>\\n      <ul>\\n        <li>Comprehensive insurance coverage</li>\\n        <li>Diverse supplier relationships</li>\\n        <li>Staff training programs</li>\\n        <li>Cash flow management</li>\\n      </ul>\\n    \",\n        isPremium: false\n    },\n    {\n        id: \"small-2\",\n        title: \"Freelance Web Development\",\n        category: \"Technology\",\n        size: \"small\",\n        description: \"A detailed business plan for starting and growing a freelance web development business.\",\n        overview: \"\\n      <h3>Executive Summary</h3>\\n      <p>This business plan outlines the establishment of a freelance web development business focused on creating custom websites and web applications for small to medium-sized businesses. The business will operate remotely with minimal overhead costs.</p>\\n\\n      <h3>Business Concept</h3>\\n      <ul>\\n        <li>Custom website development</li>\\n        <li>Web application development</li>\\n        <li>Website maintenance and support</li>\\n        <li>SEO and digital marketing services</li>\\n      </ul>\\n\\n      <h3>Target Market</h3>\\n      <ul>\\n        <li>Small businesses needing online presence</li>\\n        <li>Medium-sized companies requiring web applications</li>\\n        <li>Startups with limited budgets</li>\\n        <li>Non-profit organizations</li>\\n      </ul>\\n    \",\n        marketAnalysis: \"\\n      <h3>Market Overview</h3>\\n      <p>The web development industry continues to grow as businesses increasingly recognize the importance of online presence. Key market trends include:</p>\\n      <ul>\\n        <li>Increasing demand for mobile-responsive websites</li>\\n        <li>Growing need for e-commerce functionality</li>\\n        <li>Rising importance of user experience (UX) design</li>\\n        <li>Shift toward progressive web applications</li>\\n      </ul>\\n\\n      <h3>Competitive Analysis</h3>\\n      <p>Competition includes:</p>\\n      <ul>\\n        <li>Other freelance developers</li>\\n        <li>Web development agencies</li>\\n        <li>DIY website builders (Wix, Squarespace)</li>\\n      </ul>\\n\\n      <h3>Competitive Advantage</h3>\\n      <ul>\\n        <li>Personalized service and direct client communication</li>\\n        <li>Lower overhead costs than agencies</li>\\n        <li>Specialized expertise in modern frameworks</li>\\n        <li>Flexible pricing models</li>\\n      </ul>\\n    \",\n        financials: \"\\n      <h3>Startup Costs</h3>\\n      <ul>\\n        <li>Computer equipment: $3,000</li>\\n        <li>Software subscriptions: $1,200/year</li>\\n        <li>Website and hosting: $500</li>\\n        <li>Business registration: $300</li>\\n        <li>Initial marketing: $1,000</li>\\n      </ul>\\n\\n      <h3>Financial Projections</h3>\\n      <p>Year 1:</p>\\n      <ul>\\n        <li>Revenue: $60,000</li>\\n        <li>Expenses: $15,000</li>\\n        <li>Net profit: $45,000</li>\\n      </ul>\\n\\n      <p>Year 2:</p>\\n      <ul>\\n        <li>Revenue: $90,000</li>\\n        <li>Expenses: $20,000</li>\\n        <li>Net profit: $70,000</li>\\n      </ul>\\n    \",\n        implementation: \"\\n      <h3>Timeline</h3>\\n      <ul>\\n        <li>Month 1: Business registration and website setup</li>\\n        <li>Month 2: Portfolio development</li>\\n        <li>Month 3: Initial marketing and networking</li>\\n        <li>Month 4-6: Secure first clients and build reputation</li>\\n      </ul>\\n\\n      <h3>Marketing Strategy</h3>\\n      <ul>\\n        <li>Portfolio website showcasing work</li>\\n        <li>Social media presence on LinkedIn and Twitter</li>\\n        <li>Content marketing through blog posts</li>\\n        <li>Networking at local business events</li>\\n        <li>Referral program for existing clients</li>\\n      </ul>\\n\\n      <h3>Risk Mitigation</h3>\\n      <ul>\\n        <li>Diversify client base to avoid dependency</li>\\n        <li>Maintain emergency fund for slow periods</li>\\n        <li>Continuous skill development</li>\\n        <li>Clear contracts and scope definitions</li>\\n      </ul>\\n    \",\n        isPremium: false\n    }\n];\nconst mockBusinessPlanPreviews = mockBusinessPlans.map((plan)=>({\n        id: plan.id,\n        title: plan.title,\n        category: plan.category,\n        size: plan.size,\n        description: plan.description,\n        isPremium: plan.isPremium\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/mock-data.ts\n"));

/***/ })

});