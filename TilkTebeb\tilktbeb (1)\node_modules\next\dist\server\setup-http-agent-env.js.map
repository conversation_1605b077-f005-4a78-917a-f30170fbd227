{"version": 3, "sources": ["../../src/server/setup-http-agent-env.ts"], "sourcesContent": ["import type { NextConfig } from '../types'\nimport { Agent as HttpAgent } from 'http'\nimport { Agent as HttpsAgent } from 'https'\n\nexport function setHttpClientAndAgentOptions(config: {\n  httpAgentOptions?: NextConfig['httpAgentOptions']\n}) {\n  if (globalThis.__NEXT_HTTP_AGENT) {\n    // We only need to assign once because we want\n    // to reuse the same agent for all requests.\n    return\n  }\n\n  if (!config) {\n    throw new Error('Expected config.httpAgentOptions to be an object')\n  }\n\n  globalThis.__NEXT_HTTP_AGENT_OPTIONS = config.httpAgentOptions\n  globalThis.__NEXT_HTTP_AGENT = new HttpAgent(config.httpAgentOptions)\n  globalThis.__NEXT_HTTPS_AGENT = new HttpsAgent(config.httpAgentOptions)\n}\n"], "names": ["setHttpClientAndAgentOptions", "config", "globalThis", "__NEXT_HTTP_AGENT", "Error", "__NEXT_HTTP_AGENT_OPTIONS", "httpAgentOptions", "HttpAgent", "__NEXT_HTTPS_AGENT", "HttpsAgent"], "mappings": ";;;;+BAIgBA;;;eAAAA;;;sBAHmB;uBACC;AAE7B,SAASA,6BAA6BC,MAE5C;IACC,IAAIC,WAAWC,iBAAiB,EAAE;QAChC,8CAA8C;QAC9C,4CAA4C;QAC5C;IACF;IAEA,IAAI,CAACF,QAAQ;QACX,MAAM,IAAIG,MAAM;IAClB;IAEAF,WAAWG,yBAAyB,GAAGJ,OAAOK,gBAAgB;IAC9DJ,WAAWC,iBAAiB,GAAG,IAAII,WAAS,CAACN,OAAOK,gBAAgB;IACpEJ,WAAWM,kBAAkB,GAAG,IAAIC,YAAU,CAACR,OAAOK,gBAAgB;AACxE"}