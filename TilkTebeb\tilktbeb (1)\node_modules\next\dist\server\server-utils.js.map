{"version": 3, "sources": ["../../src/server/server-utils.ts"], "sourcesContent": ["import type { Rewrite } from '../lib/load-custom-routes'\nimport type { RouteMatchFn } from '../shared/lib/router/utils/route-matcher'\nimport type { NextConfig } from './config'\nimport type { BaseNextRequest } from './base-http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\n\nimport { format as formatUrl, parse as parseUrl } from 'url'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { getPathMatch } from '../shared/lib/router/utils/path-match'\nimport { getNamedRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getRouteMatcher } from '../shared/lib/router/utils/route-matcher'\nimport {\n  matchHas,\n  prepareDestination,\n} from '../shared/lib/router/utils/prepare-destination'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { normalizeRscURL } from '../shared/lib/router/utils/app-paths'\nimport {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../lib/constants'\n\nexport function normalizeVercelUrl(\n  req: BaseNextRequest,\n  trustQuery: boolean,\n  paramKeys?: string[],\n  pageIsDynamic?: boolean,\n  defaultRouteRegex?: ReturnType<typeof getNamedRouteRegex> | undefined\n) {\n  // make sure to normalize req.url on Vercel to strip dynamic params\n  // from the query which are added during routing\n  if (pageIsDynamic && trustQuery && defaultRouteRegex) {\n    const _parsedUrl = parseUrl(req.url!, true)\n    delete (_parsedUrl as any).search\n\n    for (const key of Object.keys(_parsedUrl.query)) {\n      const isNextQueryPrefix =\n        key !== NEXT_QUERY_PARAM_PREFIX &&\n        key.startsWith(NEXT_QUERY_PARAM_PREFIX)\n\n      const isNextInterceptionMarkerPrefix =\n        key !== NEXT_INTERCEPTION_MARKER_PREFIX &&\n        key.startsWith(NEXT_INTERCEPTION_MARKER_PREFIX)\n\n      if (\n        isNextQueryPrefix ||\n        isNextInterceptionMarkerPrefix ||\n        (paramKeys || Object.keys(defaultRouteRegex.groups)).includes(key)\n      ) {\n        delete _parsedUrl.query[key]\n      }\n    }\n    req.url = formatUrl(_parsedUrl)\n  }\n}\n\nexport function interpolateDynamicPath(\n  pathname: string,\n  params: ParsedUrlQuery,\n  defaultRouteRegex?: ReturnType<typeof getNamedRouteRegex> | undefined\n) {\n  if (!defaultRouteRegex) return pathname\n\n  for (const param of Object.keys(defaultRouteRegex.groups)) {\n    const { optional, repeat } = defaultRouteRegex.groups[param]\n    let builtParam = `[${repeat ? '...' : ''}${param}]`\n\n    if (optional) {\n      builtParam = `[${builtParam}]`\n    }\n\n    let paramValue: string\n    const value = params[param]\n\n    if (Array.isArray(value)) {\n      paramValue = value.map((v) => v && encodeURIComponent(v)).join('/')\n    } else if (value) {\n      paramValue = encodeURIComponent(value)\n    } else {\n      paramValue = ''\n    }\n\n    pathname = pathname.replaceAll(builtParam, paramValue)\n  }\n\n  return pathname\n}\n\nexport function normalizeDynamicRouteParams(\n  params: ParsedUrlQuery,\n  ignoreOptional?: boolean,\n  defaultRouteRegex?: ReturnType<typeof getNamedRouteRegex> | undefined,\n  defaultRouteMatches?: ParsedUrlQuery | undefined\n) {\n  let hasValidParams = true\n  if (!defaultRouteRegex) return { params, hasValidParams: false }\n\n  params = Object.keys(defaultRouteRegex.groups).reduce((prev, key) => {\n    let value: string | string[] | undefined = params[key]\n\n    if (typeof value === 'string') {\n      value = normalizeRscURL(value)\n    }\n    if (Array.isArray(value)) {\n      value = value.map((val) => {\n        if (typeof val === 'string') {\n          val = normalizeRscURL(val)\n        }\n        return val\n      })\n    }\n\n    // if the value matches the default value we can't rely\n    // on the parsed params, this is used to signal if we need\n    // to parse x-now-route-matches or not\n    const defaultValue = defaultRouteMatches![key]\n    const isOptional = defaultRouteRegex!.groups[key].optional\n\n    const isDefaultValue = Array.isArray(defaultValue)\n      ? defaultValue.some((defaultVal) => {\n          return Array.isArray(value)\n            ? value.some((val) => val.includes(defaultVal))\n            : value?.includes(defaultVal)\n        })\n      : value?.includes(defaultValue as string)\n\n    if (\n      isDefaultValue ||\n      (typeof value === 'undefined' && !(isOptional && ignoreOptional))\n    ) {\n      hasValidParams = false\n    }\n\n    // non-provided optional values should be undefined so normalize\n    // them to undefined\n    if (\n      isOptional &&\n      (!value ||\n        (Array.isArray(value) &&\n          value.length === 1 &&\n          // fallback optional catch-all SSG pages have\n          // [[...paramName]] for the root path on Vercel\n          (value[0] === 'index' || value[0] === `[[...${key}]]`)))\n    ) {\n      value = undefined\n      delete params[key]\n    }\n\n    // query values from the proxy aren't already split into arrays\n    // so make sure to normalize catch-all values\n    if (\n      value &&\n      typeof value === 'string' &&\n      defaultRouteRegex!.groups[key].repeat\n    ) {\n      value = value.split('/')\n    }\n\n    if (value) {\n      prev[key] = value\n    }\n    return prev\n  }, {} as ParsedUrlQuery)\n\n  return {\n    params,\n    hasValidParams,\n  }\n}\n\nexport function getUtils({\n  page,\n  i18n,\n  basePath,\n  rewrites,\n  pageIsDynamic,\n  trailingSlash,\n  caseSensitive,\n}: {\n  page: string\n  i18n?: NextConfig['i18n']\n  basePath: string\n  rewrites: {\n    fallback?: ReadonlyArray<Rewrite>\n    afterFiles?: ReadonlyArray<Rewrite>\n    beforeFiles?: ReadonlyArray<Rewrite>\n  }\n  pageIsDynamic: boolean\n  trailingSlash?: boolean\n  caseSensitive: boolean\n}) {\n  let defaultRouteRegex: ReturnType<typeof getNamedRouteRegex> | undefined\n  let dynamicRouteMatcher: RouteMatchFn | undefined\n  let defaultRouteMatches: ParsedUrlQuery | undefined\n\n  if (pageIsDynamic) {\n    defaultRouteRegex = getNamedRouteRegex(page, false)\n    dynamicRouteMatcher = getRouteMatcher(defaultRouteRegex)\n    defaultRouteMatches = dynamicRouteMatcher(page) as ParsedUrlQuery\n  }\n\n  function handleRewrites(req: BaseNextRequest, parsedUrl: UrlWithParsedQuery) {\n    const rewriteParams = {}\n    let fsPathname = parsedUrl.pathname\n\n    const matchesPage = () => {\n      const fsPathnameNoSlash = removeTrailingSlash(fsPathname || '')\n      return (\n        fsPathnameNoSlash === removeTrailingSlash(page) ||\n        dynamicRouteMatcher?.(fsPathnameNoSlash)\n      )\n    }\n\n    const checkRewrite = (rewrite: Rewrite): boolean => {\n      const matcher = getPathMatch(\n        rewrite.source + (trailingSlash ? '(/)?' : ''),\n        {\n          removeUnnamedParams: true,\n          strict: true,\n          sensitive: !!caseSensitive,\n        }\n      )\n      let params = matcher(parsedUrl.pathname)\n\n      if ((rewrite.has || rewrite.missing) && params) {\n        const hasParams = matchHas(\n          req,\n          parsedUrl.query,\n          rewrite.has,\n          rewrite.missing\n        )\n\n        if (hasParams) {\n          Object.assign(params, hasParams)\n        } else {\n          params = false\n        }\n      }\n\n      if (params) {\n        const { parsedDestination, destQuery } = prepareDestination({\n          appendParamsToQuery: true,\n          destination: rewrite.destination,\n          params: params,\n          query: parsedUrl.query,\n        })\n\n        // if the rewrite destination is external break rewrite chain\n        if (parsedDestination.protocol) {\n          return true\n        }\n\n        Object.assign(rewriteParams, destQuery, params)\n        Object.assign(parsedUrl.query, parsedDestination.query)\n        delete (parsedDestination as any).query\n\n        Object.assign(parsedUrl, parsedDestination)\n\n        fsPathname = parsedUrl.pathname\n\n        if (basePath) {\n          fsPathname =\n            fsPathname!.replace(new RegExp(`^${basePath}`), '') || '/'\n        }\n\n        if (i18n) {\n          const destLocalePathResult = normalizeLocalePath(\n            fsPathname!,\n            i18n.locales\n          )\n          fsPathname = destLocalePathResult.pathname\n          parsedUrl.query.nextInternalLocale =\n            destLocalePathResult.detectedLocale || params.nextInternalLocale\n        }\n\n        if (fsPathname === page) {\n          return true\n        }\n\n        if (pageIsDynamic && dynamicRouteMatcher) {\n          const dynamicParams = dynamicRouteMatcher(fsPathname)\n          if (dynamicParams) {\n            parsedUrl.query = {\n              ...parsedUrl.query,\n              ...dynamicParams,\n            }\n            return true\n          }\n        }\n      }\n      return false\n    }\n\n    for (const rewrite of rewrites.beforeFiles || []) {\n      checkRewrite(rewrite)\n    }\n\n    if (fsPathname !== page) {\n      let finished = false\n\n      for (const rewrite of rewrites.afterFiles || []) {\n        finished = checkRewrite(rewrite)\n        if (finished) break\n      }\n\n      if (!finished && !matchesPage()) {\n        for (const rewrite of rewrites.fallback || []) {\n          finished = checkRewrite(rewrite)\n          if (finished) break\n        }\n      }\n    }\n    return rewriteParams\n  }\n\n  function getParamsFromRouteMatches(\n    req: BaseNextRequest,\n    renderOpts?: any,\n    detectedLocale?: string\n  ) {\n    return getRouteMatcher(\n      (function () {\n        const { groups, routeKeys } = defaultRouteRegex!\n\n        return {\n          re: {\n            // Simulate a RegExp match from the \\`req.url\\` input\n            exec: (str: string) => {\n              const obj = Object.fromEntries(new URLSearchParams(str))\n              const matchesHasLocale =\n                i18n && detectedLocale && obj['1'] === detectedLocale\n\n              for (const key of Object.keys(obj)) {\n                const value = obj[key]\n\n                if (\n                  key !== NEXT_QUERY_PARAM_PREFIX &&\n                  key.startsWith(NEXT_QUERY_PARAM_PREFIX)\n                ) {\n                  const normalizedKey = key.substring(\n                    NEXT_QUERY_PARAM_PREFIX.length\n                  )\n                  obj[normalizedKey] = value\n                  delete obj[key]\n                }\n              }\n\n              // favor named matches if available\n              const routeKeyNames = Object.keys(routeKeys || {})\n              const filterLocaleItem = (val: string | string[] | undefined) => {\n                if (i18n) {\n                  // locale items can be included in route-matches\n                  // for fallback SSG pages so ensure they are\n                  // filtered\n                  const isCatchAll = Array.isArray(val)\n                  const _val = isCatchAll ? val[0] : val\n\n                  if (\n                    typeof _val === 'string' &&\n                    i18n.locales.some((item) => {\n                      if (item.toLowerCase() === _val.toLowerCase()) {\n                        detectedLocale = item\n                        renderOpts.locale = detectedLocale\n                        return true\n                      }\n                      return false\n                    })\n                  ) {\n                    // remove the locale item from the match\n                    if (isCatchAll) {\n                      ;(val as string[]).splice(0, 1)\n                    }\n\n                    // the value is only a locale item and\n                    // shouldn't be added\n                    return isCatchAll ? val.length === 0 : true\n                  }\n                }\n                return false\n              }\n\n              if (routeKeyNames.every((name) => obj[name])) {\n                return routeKeyNames.reduce((prev, keyName) => {\n                  const paramName = routeKeys?.[keyName]\n\n                  if (paramName && !filterLocaleItem(obj[keyName])) {\n                    prev[groups[paramName].pos] = obj[keyName]\n                  }\n                  return prev\n                }, {} as any)\n              }\n\n              return Object.keys(obj).reduce((prev, key) => {\n                if (!filterLocaleItem(obj[key])) {\n                  let normalizedKey = key\n\n                  if (matchesHasLocale) {\n                    normalizedKey = parseInt(key, 10) - 1 + ''\n                  }\n                  return Object.assign(prev, {\n                    [normalizedKey]: obj[key],\n                  })\n                }\n                return prev\n              }, {})\n            },\n          },\n          groups,\n        }\n      })() as any\n    )(req.headers['x-now-route-matches'] as string) as ParsedUrlQuery\n  }\n\n  return {\n    handleRewrites,\n    defaultRouteRegex,\n    dynamicRouteMatcher,\n    defaultRouteMatches,\n    getParamsFromRouteMatches,\n    normalizeDynamicRouteParams: (\n      params: ParsedUrlQuery,\n      ignoreOptional?: boolean\n    ) =>\n      normalizeDynamicRouteParams(\n        params,\n        ignoreOptional,\n        defaultRouteRegex,\n        defaultRouteMatches\n      ),\n    normalizeVercelUrl: (\n      req: BaseNextRequest,\n      trustQuery: boolean,\n      paramKeys?: string[]\n    ) =>\n      normalizeVercelUrl(\n        req,\n        trustQuery,\n        paramKeys,\n        pageIsDynamic,\n        defaultRouteRegex\n      ),\n    interpolateDynamicPath: (\n      pathname: string,\n      params: Record<string, undefined | string | string[]>\n    ) => interpolateDynamicPath(pathname, params, defaultRouteRegex),\n  }\n}\n"], "names": ["getUtils", "interpolateDynamicPath", "normalizeDynamicRouteParams", "normalizeVercelUrl", "req", "trustQuery", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pageIsDynamic", "defaultRouteRegex", "_parsedUrl", "parseUrl", "url", "search", "key", "Object", "keys", "query", "isNextQueryPrefix", "NEXT_QUERY_PARAM_PREFIX", "startsWith", "isNextInterceptionMarkerPrefix", "NEXT_INTERCEPTION_MARKER_PREFIX", "groups", "includes", "formatUrl", "pathname", "params", "param", "optional", "repeat", "builtParam", "paramValue", "value", "Array", "isArray", "map", "v", "encodeURIComponent", "join", "replaceAll", "ignoreOptional", "defaultRouteMatches", "hasValidParams", "reduce", "prev", "normalizeRscURL", "val", "defaultValue", "isOptional", "isDefaultValue", "some", "defaultVal", "length", "undefined", "split", "page", "i18n", "basePath", "rewrites", "trailingSlash", "caseSensitive", "dynamicRouteMatcher", "getNamedRouteRegex", "getRouteMatcher", "handleRewrites", "parsedUrl", "rewriteParams", "fsPathname", "matchesPage", "fsPathnameNoSlash", "removeTrailingSlash", "checkRewrite", "rewrite", "matcher", "getPathMatch", "source", "removeUnnamedP<PERSON>ms", "strict", "sensitive", "has", "missing", "hasParams", "matchHas", "assign", "parsedDestination", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prepareDestination", "appendParamsToQuery", "destination", "protocol", "replace", "RegExp", "destLocalePathResult", "normalizeLocalePath", "locales", "nextInternalLocale", "detectedLocale", "dynamicParams", "beforeFiles", "finished", "afterFiles", "fallback", "getParamsFromRouteMatches", "renderOpts", "routeKeys", "re", "exec", "str", "obj", "fromEntries", "URLSearchParams", "matchesHasLocale", "normalizedKey", "substring", "routeKeyNames", "filterLocaleItem", "isCatchAll", "_val", "item", "toLowerCase", "locale", "splice", "every", "name", "keyName", "paramName", "pos", "parseInt", "headers"], "mappings": ";;;;;;;;;;;;;;;;;IA2KgBA,QAAQ;eAARA;;IAlHAC,sBAAsB;eAAtBA;;IAgCAC,2BAA2B;eAA3BA;;IAlEAC,kBAAkB;eAAlBA;;;qBAhBuC;qCACnB;2BACP;4BACM;8BACH;oCAIzB;qCAC6B;0BACJ;2BAIzB;AAEA,SAASA,mBACdC,GAAoB,EACpBC,UAAmB,EACnBC,SAAoB,EACpBC,aAAuB,EACvBC,iBAAqE;IAErE,mEAAmE;IACnE,gDAAgD;IAChD,IAAID,iBAAiBF,cAAcG,mBAAmB;QACpD,MAAMC,aAAaC,IAAAA,UAAQ,EAACN,IAAIO,GAAG,EAAG;QACtC,OAAO,AAACF,WAAmBG,MAAM;QAEjC,KAAK,MAAMC,OAAOC,OAAOC,IAAI,CAACN,WAAWO,KAAK,EAAG;YAC/C,MAAMC,oBACJJ,QAAQK,kCAAuB,IAC/BL,IAAIM,UAAU,CAACD,kCAAuB;YAExC,MAAME,iCACJP,QAAQQ,0CAA+B,IACvCR,IAAIM,UAAU,CAACE,0CAA+B;YAEhD,IACEJ,qBACAG,kCACA,AAACd,CAAAA,aAAaQ,OAAOC,IAAI,CAACP,kBAAkBc,MAAM,CAAA,EAAGC,QAAQ,CAACV,MAC9D;gBACA,OAAOJ,WAAWO,KAAK,CAACH,IAAI;YAC9B;QACF;QACAT,IAAIO,GAAG,GAAGa,IAAAA,WAAS,EAACf;IACtB;AACF;AAEO,SAASR,uBACdwB,QAAgB,EAChBC,MAAsB,EACtBlB,iBAAqE;IAErE,IAAI,CAACA,mBAAmB,OAAOiB;IAE/B,KAAK,MAAME,SAASb,OAAOC,IAAI,CAACP,kBAAkBc,MAAM,EAAG;QACzD,MAAM,EAAEM,QAAQ,EAAEC,MAAM,EAAE,GAAGrB,kBAAkBc,MAAM,CAACK,MAAM;QAC5D,IAAIG,aAAa,CAAC,CAAC,EAAED,SAAS,QAAQ,KAAKF,MAAM,CAAC,CAAC;QAEnD,IAAIC,UAAU;YACZE,aAAa,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC;QAChC;QAEA,IAAIC;QACJ,MAAMC,QAAQN,MAAM,CAACC,MAAM;QAE3B,IAAIM,MAAMC,OAAO,CAACF,QAAQ;YACxBD,aAAaC,MAAMG,GAAG,CAAC,CAACC,IAAMA,KAAKC,mBAAmBD,IAAIE,IAAI,CAAC;QACjE,OAAO,IAAIN,OAAO;YAChBD,aAAaM,mBAAmBL;QAClC,OAAO;YACLD,aAAa;QACf;QAEAN,WAAWA,SAASc,UAAU,CAACT,YAAYC;IAC7C;IAEA,OAAON;AACT;AAEO,SAASvB,4BACdwB,MAAsB,EACtBc,cAAwB,EACxBhC,iBAAqE,EACrEiC,mBAAgD;IAEhD,IAAIC,iBAAiB;IACrB,IAAI,CAAClC,mBAAmB,OAAO;QAAEkB;QAAQgB,gBAAgB;IAAM;IAE/DhB,SAASZ,OAAOC,IAAI,CAACP,kBAAkBc,MAAM,EAAEqB,MAAM,CAAC,CAACC,MAAM/B;QAC3D,IAAImB,QAAuCN,MAAM,CAACb,IAAI;QAEtD,IAAI,OAAOmB,UAAU,UAAU;YAC7BA,QAAQa,IAAAA,yBAAe,EAACb;QAC1B;QACA,IAAIC,MAAMC,OAAO,CAACF,QAAQ;YACxBA,QAAQA,MAAMG,GAAG,CAAC,CAACW;gBACjB,IAAI,OAAOA,QAAQ,UAAU;oBAC3BA,MAAMD,IAAAA,yBAAe,EAACC;gBACxB;gBACA,OAAOA;YACT;QACF;QAEA,uDAAuD;QACvD,0DAA0D;QAC1D,sCAAsC;QACtC,MAAMC,eAAeN,mBAAoB,CAAC5B,IAAI;QAC9C,MAAMmC,aAAaxC,kBAAmBc,MAAM,CAACT,IAAI,CAACe,QAAQ;QAE1D,MAAMqB,iBAAiBhB,MAAMC,OAAO,CAACa,gBACjCA,aAAaG,IAAI,CAAC,CAACC;YACjB,OAAOlB,MAAMC,OAAO,CAACF,SACjBA,MAAMkB,IAAI,CAAC,CAACJ,MAAQA,IAAIvB,QAAQ,CAAC4B,eACjCnB,yBAAAA,MAAOT,QAAQ,CAAC4B;QACtB,KACAnB,yBAAAA,MAAOT,QAAQ,CAACwB;QAEpB,IACEE,kBACC,OAAOjB,UAAU,eAAe,CAAEgB,CAAAA,cAAcR,cAAa,GAC9D;YACAE,iBAAiB;QACnB;QAEA,gEAAgE;QAChE,oBAAoB;QACpB,IACEM,cACC,CAAA,CAAChB,SACCC,MAAMC,OAAO,CAACF,UACbA,MAAMoB,MAAM,KAAK,KACjB,6CAA6C;QAC7C,+CAA+C;QAC9CpB,CAAAA,KAAK,CAAC,EAAE,KAAK,WAAWA,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAEnB,IAAI,EAAE,CAAC,AAAD,CAAE,GAC1D;YACAmB,QAAQqB;YACR,OAAO3B,MAAM,CAACb,IAAI;QACpB;QAEA,+DAA+D;QAC/D,6CAA6C;QAC7C,IACEmB,SACA,OAAOA,UAAU,YACjBxB,kBAAmBc,MAAM,CAACT,IAAI,CAACgB,MAAM,EACrC;YACAG,QAAQA,MAAMsB,KAAK,CAAC;QACtB;QAEA,IAAItB,OAAO;YACTY,IAAI,CAAC/B,IAAI,GAAGmB;QACd;QACA,OAAOY;IACT,GAAG,CAAC;IAEJ,OAAO;QACLlB;QACAgB;IACF;AACF;AAEO,SAAS1C,SAAS,EACvBuD,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRnD,aAAa,EACboD,aAAa,EACbC,aAAa,EAad;IACC,IAAIpD;IACJ,IAAIqD;IACJ,IAAIpB;IAEJ,IAAIlC,eAAe;QACjBC,oBAAoBsD,IAAAA,8BAAkB,EAACP,MAAM;QAC7CM,sBAAsBE,IAAAA,6BAAe,EAACvD;QACtCiC,sBAAsBoB,oBAAoBN;IAC5C;IAEA,SAASS,eAAe5D,GAAoB,EAAE6D,SAA6B;QACzE,MAAMC,gBAAgB,CAAC;QACvB,IAAIC,aAAaF,UAAUxC,QAAQ;QAEnC,MAAM2C,cAAc;YAClB,MAAMC,oBAAoBC,IAAAA,wCAAmB,EAACH,cAAc;YAC5D,OACEE,sBAAsBC,IAAAA,wCAAmB,EAACf,UAC1CM,uCAAAA,oBAAsBQ;QAE1B;QAEA,MAAME,eAAe,CAACC;YACpB,MAAMC,UAAUC,IAAAA,uBAAY,EAC1BF,QAAQG,MAAM,GAAIhB,CAAAA,gBAAgB,SAAS,EAAC,GAC5C;gBACEiB,qBAAqB;gBACrBC,QAAQ;gBACRC,WAAW,CAAC,CAAClB;YACf;YAEF,IAAIlC,SAAS+C,QAAQR,UAAUxC,QAAQ;YAEvC,IAAI,AAAC+C,CAAAA,QAAQO,GAAG,IAAIP,QAAQQ,OAAO,AAAD,KAAMtD,QAAQ;gBAC9C,MAAMuD,YAAYC,IAAAA,4BAAQ,EACxB9E,KACA6D,UAAUjD,KAAK,EACfwD,QAAQO,GAAG,EACXP,QAAQQ,OAAO;gBAGjB,IAAIC,WAAW;oBACbnE,OAAOqE,MAAM,CAACzD,QAAQuD;gBACxB,OAAO;oBACLvD,SAAS;gBACX;YACF;YAEA,IAAIA,QAAQ;gBACV,MAAM,EAAE0D,iBAAiB,EAAEC,SAAS,EAAE,GAAGC,IAAAA,sCAAkB,EAAC;oBAC1DC,qBAAqB;oBACrBC,aAAahB,QAAQgB,WAAW;oBAChC9D,QAAQA;oBACRV,OAAOiD,UAAUjD,KAAK;gBACxB;gBAEA,6DAA6D;gBAC7D,IAAIoE,kBAAkBK,QAAQ,EAAE;oBAC9B,OAAO;gBACT;gBAEA3E,OAAOqE,MAAM,CAACjB,eAAemB,WAAW3D;gBACxCZ,OAAOqE,MAAM,CAAClB,UAAUjD,KAAK,EAAEoE,kBAAkBpE,KAAK;gBACtD,OAAO,AAACoE,kBAA0BpE,KAAK;gBAEvCF,OAAOqE,MAAM,CAAClB,WAAWmB;gBAEzBjB,aAAaF,UAAUxC,QAAQ;gBAE/B,IAAIgC,UAAU;oBACZU,aACEA,WAAYuB,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAElC,UAAU,GAAG,OAAO;gBAC3D;gBAEA,IAAID,MAAM;oBACR,MAAMoC,uBAAuBC,IAAAA,wCAAmB,EAC9C1B,YACAX,KAAKsC,OAAO;oBAEd3B,aAAayB,qBAAqBnE,QAAQ;oBAC1CwC,UAAUjD,KAAK,CAAC+E,kBAAkB,GAChCH,qBAAqBI,cAAc,IAAItE,OAAOqE,kBAAkB;gBACpE;gBAEA,IAAI5B,eAAeZ,MAAM;oBACvB,OAAO;gBACT;gBAEA,IAAIhD,iBAAiBsD,qBAAqB;oBACxC,MAAMoC,gBAAgBpC,oBAAoBM;oBAC1C,IAAI8B,eAAe;wBACjBhC,UAAUjD,KAAK,GAAG;4BAChB,GAAGiD,UAAUjD,KAAK;4BAClB,GAAGiF,aAAa;wBAClB;wBACA,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,KAAK,MAAMzB,WAAWd,SAASwC,WAAW,IAAI,EAAE,CAAE;YAChD3B,aAAaC;QACf;QAEA,IAAIL,eAAeZ,MAAM;YACvB,IAAI4C,WAAW;YAEf,KAAK,MAAM3B,WAAWd,SAAS0C,UAAU,IAAI,EAAE,CAAE;gBAC/CD,WAAW5B,aAAaC;gBACxB,IAAI2B,UAAU;YAChB;YAEA,IAAI,CAACA,YAAY,CAAC/B,eAAe;gBAC/B,KAAK,MAAMI,WAAWd,SAAS2C,QAAQ,IAAI,EAAE,CAAE;oBAC7CF,WAAW5B,aAAaC;oBACxB,IAAI2B,UAAU;gBAChB;YACF;QACF;QACA,OAAOjC;IACT;IAEA,SAASoC,0BACPlG,GAAoB,EACpBmG,UAAgB,EAChBP,cAAuB;QAEvB,OAAOjC,IAAAA,6BAAe,EACpB,AAAC;YACC,MAAM,EAAEzC,MAAM,EAAEkF,SAAS,EAAE,GAAGhG;YAE9B,OAAO;gBACLiG,IAAI;oBACF,qDAAqD;oBACrDC,MAAM,CAACC;wBACL,MAAMC,MAAM9F,OAAO+F,WAAW,CAAC,IAAIC,gBAAgBH;wBACnD,MAAMI,mBACJvD,QAAQwC,kBAAkBY,GAAG,CAAC,IAAI,KAAKZ;wBAEzC,KAAK,MAAMnF,OAAOC,OAAOC,IAAI,CAAC6F,KAAM;4BAClC,MAAM5E,QAAQ4E,GAAG,CAAC/F,IAAI;4BAEtB,IACEA,QAAQK,kCAAuB,IAC/BL,IAAIM,UAAU,CAACD,kCAAuB,GACtC;gCACA,MAAM8F,gBAAgBnG,IAAIoG,SAAS,CACjC/F,kCAAuB,CAACkC,MAAM;gCAEhCwD,GAAG,CAACI,cAAc,GAAGhF;gCACrB,OAAO4E,GAAG,CAAC/F,IAAI;4BACjB;wBACF;wBAEA,mCAAmC;wBACnC,MAAMqG,gBAAgBpG,OAAOC,IAAI,CAACyF,aAAa,CAAC;wBAChD,MAAMW,mBAAmB,CAACrE;4BACxB,IAAIU,MAAM;gCACR,gDAAgD;gCAChD,4CAA4C;gCAC5C,WAAW;gCACX,MAAM4D,aAAanF,MAAMC,OAAO,CAACY;gCACjC,MAAMuE,OAAOD,aAAatE,GAAG,CAAC,EAAE,GAAGA;gCAEnC,IACE,OAAOuE,SAAS,YAChB7D,KAAKsC,OAAO,CAAC5C,IAAI,CAAC,CAACoE;oCACjB,IAAIA,KAAKC,WAAW,OAAOF,KAAKE,WAAW,IAAI;wCAC7CvB,iBAAiBsB;wCACjBf,WAAWiB,MAAM,GAAGxB;wCACpB,OAAO;oCACT;oCACA,OAAO;gCACT,IACA;oCACA,wCAAwC;oCACxC,IAAIoB,YAAY;;wCACZtE,IAAiB2E,MAAM,CAAC,GAAG;oCAC/B;oCAEA,sCAAsC;oCACtC,qBAAqB;oCACrB,OAAOL,aAAatE,IAAIM,MAAM,KAAK,IAAI;gCACzC;4BACF;4BACA,OAAO;wBACT;wBAEA,IAAI8D,cAAcQ,KAAK,CAAC,CAACC,OAASf,GAAG,CAACe,KAAK,GAAG;4BAC5C,OAAOT,cAAcvE,MAAM,CAAC,CAACC,MAAMgF;gCACjC,MAAMC,YAAYrB,6BAAAA,SAAW,CAACoB,QAAQ;gCAEtC,IAAIC,aAAa,CAACV,iBAAiBP,GAAG,CAACgB,QAAQ,GAAG;oCAChDhF,IAAI,CAACtB,MAAM,CAACuG,UAAU,CAACC,GAAG,CAAC,GAAGlB,GAAG,CAACgB,QAAQ;gCAC5C;gCACA,OAAOhF;4BACT,GAAG,CAAC;wBACN;wBAEA,OAAO9B,OAAOC,IAAI,CAAC6F,KAAKjE,MAAM,CAAC,CAACC,MAAM/B;4BACpC,IAAI,CAACsG,iBAAiBP,GAAG,CAAC/F,IAAI,GAAG;gCAC/B,IAAImG,gBAAgBnG;gCAEpB,IAAIkG,kBAAkB;oCACpBC,gBAAgBe,SAASlH,KAAK,MAAM,IAAI;gCAC1C;gCACA,OAAOC,OAAOqE,MAAM,CAACvC,MAAM;oCACzB,CAACoE,cAAc,EAAEJ,GAAG,CAAC/F,IAAI;gCAC3B;4BACF;4BACA,OAAO+B;wBACT,GAAG,CAAC;oBACN;gBACF;gBACAtB;YACF;QACF,KACAlB,IAAI4H,OAAO,CAAC,sBAAsB;IACtC;IAEA,OAAO;QACLhE;QACAxD;QACAqD;QACApB;QACA6D;QACApG,6BAA6B,CAC3BwB,QACAc,iBAEAtC,4BACEwB,QACAc,gBACAhC,mBACAiC;QAEJtC,oBAAoB,CAClBC,KACAC,YACAC,YAEAH,mBACEC,KACAC,YACAC,WACAC,eACAC;QAEJP,wBAAwB,CACtBwB,UACAC,SACGzB,uBAAuBwB,UAAUC,QAAQlB;IAChD;AACF"}