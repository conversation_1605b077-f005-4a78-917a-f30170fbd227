globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/bookmarks/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/navbar.tsx":{"*":{"id":"(ssr)/./components/navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/offline-detector.tsx":{"*":{"id":"(ssr)/./components/offline-detector.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/particles-background.tsx":{"*":{"id":"(ssr)/./components/particles-background.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sidebar.tsx":{"*":{"id":"(ssr)/./components/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(ssr)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(ssr)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/hero-section.tsx":{"*":{"id":"(ssr)/./components/hero-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/business-plans/page.tsx":{"*":{"id":"(ssr)/./app/business-plans/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/books/page.tsx":{"*":{"id":"(ssr)/./app/books/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/books/[slug]/page.tsx":{"*":{"id":"(ssr)/./app/books/[slug]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/bookmarks-list.tsx":{"*":{"id":"(ssr)/./components/bookmarks-list.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/offline-library/page.tsx":{"*":{"id":"(ssr)/./app/offline-library/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\navbar.tsx":{"id":"(app-pages-browser)/./components/navbar.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\offline-detector.tsx":{"id":"(app-pages-browser)/./components/offline-detector.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\particles-background.tsx":{"id":"(app-pages-browser)/./components/particles-background.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\sidebar.tsx":{"id":"(app-pages-browser)/./components/sidebar.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\theme-provider.tsx":{"id":"(app-pages-browser)/./components/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/bookmarks/page","static/chunks/app/bookmarks/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/bookmarks/page","static/chunks/app/bookmarks/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\hero-section.tsx":{"id":"(app-pages-browser)/./components/hero-section.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\dist\\lib\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\node_modules\\next\\dist\\esm\\lib\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\business-plans\\page.tsx":{"id":"(app-pages-browser)/./app/business-plans/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\books\\page.tsx":{"id":"(app-pages-browser)/./app/books/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\books\\[slug]\\page.tsx":{"id":"(app-pages-browser)/./app/books/[slug]/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\bookmarks-list.tsx":{"id":"(app-pages-browser)/./components/bookmarks-list.tsx","name":"*","chunks":["app/bookmarks/page","static/chunks/app/bookmarks/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\offline-library\\page.tsx":{"id":"(app-pages-browser)/./app/offline-library/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\":[],"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\bookmarks\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/navbar.tsx":{"*":{"id":"(rsc)/./components/navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/offline-detector.tsx":{"*":{"id":"(rsc)/./components/offline-detector.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/particles-background.tsx":{"*":{"id":"(rsc)/./components/particles-background.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sidebar.tsx":{"*":{"id":"(rsc)/./components/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(rsc)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(rsc)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/hero-section.tsx":{"*":{"id":"(rsc)/./components/hero-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/business-plans/page.tsx":{"*":{"id":"(rsc)/./app/business-plans/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/books/page.tsx":{"*":{"id":"(rsc)/./app/books/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/books/[slug]/page.tsx":{"*":{"id":"(rsc)/./app/books/[slug]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/bookmarks-list.tsx":{"*":{"id":"(rsc)/./components/bookmarks-list.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/offline-library/page.tsx":{"*":{"id":"(rsc)/./app/offline-library/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}