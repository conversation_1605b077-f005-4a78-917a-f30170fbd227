{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-no-store.ts"], "sourcesContent": ["import { workAsyncStorage } from '../../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../../app-render/work-unit-async-storage.external'\nimport { markCurrentScopeAsDynamic } from '../../app-render/dynamic-rendering'\n\n/**\n * This function can be used to declaratively opt out of static rendering and indicate a particular component should not be cached.\n *\n * It marks the current scope as dynamic.\n *\n * - In [non-PPR](https://nextjs.org/docs/app/api-reference/next-config-js/partial-prerendering) cases this will make a static render\n * halt and mark the page as dynamic.\n * - In PPR cases this will postpone the render at this location.\n *\n * If we are inside a cache scope then this function does nothing.\n *\n * @note It expects to be called within App Router and will error otherwise.\n *\n * Read more: [Next.js Docs: `unstable_noStore`](https://nextjs.org/docs/app/api-reference/functions/unstable_noStore)\n */\nexport function unstable_noStore() {\n  const callingExpression = 'unstable_noStore()'\n  const store = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (!store) {\n    // This generally implies we are being called in Pages router. We should probably not support\n    // unstable_noStore in contexts outside of `react-server` condition but since we historically\n    // have not errored here previously, we maintain that behavior for now.\n    return\n  } else if (store.forceStatic) {\n    return\n  } else {\n    store.isUnstableNoStore = true\n    if (workUnitStore && workUnitStore.type === 'prerender') {\n      // unstable_noStore() is a noop in Dynamic I/O.\n    } else {\n      markCurrentScopeAsDynamic(store, workUnitStore, callingExpression)\n    }\n  }\n}\n"], "names": ["unstable_noStore", "callingExpression", "store", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "forceStatic", "isUnstableNoStore", "type", "markCurrentScopeAsDynamic"], "mappings": ";;;;+BAmBgBA;;;eAAAA;;;0CAnBiB;8CACI;kCACK;AAiBnC,SAASA;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,QAAQC,0CAAgB,CAACC,QAAQ;IACvC,MAAMC,gBAAgBC,kDAAoB,CAACF,QAAQ;IACnD,IAAI,CAACF,OAAO;QACV,6FAA6F;QAC7F,6FAA6F;QAC7F,uEAAuE;QACvE;IACF,OAAO,IAAIA,MAAMK,WAAW,EAAE;QAC5B;IACF,OAAO;QACLL,MAAMM,iBAAiB,GAAG;QAC1B,IAAIH,iBAAiBA,cAAcI,IAAI,KAAK,aAAa;QACvD,+CAA+C;QACjD,OAAO;YACLC,IAAAA,2CAAyB,EAACR,OAAOG,eAAeJ;QAClD;IACF;AACF"}