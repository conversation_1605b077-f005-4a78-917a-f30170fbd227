"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/books/[slug]/page",{

/***/ "(app-pages-browser)/./app/books/[slug]/page.tsx":
/*!***********************************!*\
  !*** ./app/books/[slug]/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BookPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _components_book_bookmark__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/book-bookmark */ \"(app-pages-browser)/./components/book-bookmark.tsx\");\n/* harmony import */ var _components_offline_book_toggle__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/offline-book-toggle */ \"(app-pages-browser)/./components/offline-book-toggle.tsx\");\n/* harmony import */ var _lib_offline_storage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/offline-storage */ \"(app-pages-browser)/./lib/offline-storage.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _components_reading_progress_bar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/reading-progress-bar */ \"(app-pages-browser)/./components/reading-progress-bar.tsx\");\n/* harmony import */ var _components_highlight_controls__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/highlight-controls */ \"(app-pages-browser)/./components/highlight-controls.tsx\");\n/* harmony import */ var _components_notes_panel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/notes-panel */ \"(app-pages-browser)/./components/notes-panel.tsx\");\n/* harmony import */ var _components_text_to_speech_player__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/text-to-speech-player */ \"(app-pages-browser)/./components/text-to-speech-player.tsx\");\n/* harmony import */ var _components_flashcard_creator__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/flashcard-creator */ \"(app-pages-browser)/./components/flashcard-creator.tsx\");\n/* harmony import */ var _components_reading_speed_tracker__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/reading-speed-tracker */ \"(app-pages-browser)/./components/reading-speed-tracker.tsx\");\n/* harmony import */ var _components_social_sharing__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/social-sharing */ \"(app-pages-browser)/./components/social-sharing.tsx\");\n/* harmony import */ var _components_comprehension_quiz__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/comprehension-quiz */ \"(app-pages-browser)/./components/comprehension-quiz.tsx\");\n/* harmony import */ var _components_recommendations_similar_content__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/recommendations/similar-content */ \"(app-pages-browser)/./components/recommendations/similar-content.tsx\");\n/* harmony import */ var _lib_analytics__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/lib/analytics */ \"(app-pages-browser)/./lib/analytics.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction BookPage(param) {\n    let { params } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [book, setBook] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [fontSize, setFontSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"medium\");\n    const [isOffline, setIsOffline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOfflineAvailable, setIsOfflineAvailable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [readingProgress, setReadingProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showNotes, setShowNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isHighlighting, setIsHighlighting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"reading\");\n    const [selectedTextForSharing, setSelectedTextForSharing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isTtsPlaying, setIsTtsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [readingStartTime, setReadingStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [readingSessionId, setReadingSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Check online status\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BookPage.useEffect\": ()=>{\n            setIsOffline(!navigator.onLine);\n            const handleOnline = {\n                \"BookPage.useEffect.handleOnline\": ()=>setIsOffline(false)\n            }[\"BookPage.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"BookPage.useEffect.handleOffline\": ()=>setIsOffline(true)\n            }[\"BookPage.useEffect.handleOffline\"];\n            window.addEventListener(\"online\", handleOnline);\n            window.addEventListener(\"offline\", handleOffline);\n            return ({\n                \"BookPage.useEffect\": ()=>{\n                    window.removeEventListener(\"online\", handleOnline);\n                    window.removeEventListener(\"offline\", handleOffline);\n                }\n            })[\"BookPage.useEffect\"];\n        }\n    }[\"BookPage.useEffect\"], []);\n    // Fetch book data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BookPage.useEffect\": ()=>{\n            const fetchBook = {\n                \"BookPage.useEffect.fetchBook\": async ()=>{\n                    try {\n                        var _bookData_categories;\n                        setIsLoading(true);\n                        // First check if book is available offline\n                        const offlineAvailable = await (0,_lib_offline_storage__WEBPACK_IMPORTED_MODULE_9__.isBookAvailableOffline)(params.slug);\n                        setIsOfflineAvailable(offlineAvailable);\n                        if (isOffline && !offlineAvailable) {\n                            // If offline and book not available offline\n                            setError(\"You're offline and this book isn't available offline. Please connect to the internet or choose a book you've saved for offline reading.\");\n                            setIsLoading(false);\n                            return;\n                        }\n                        let bookData = null;\n                        if (offlineAvailable) {\n                            // Get from offline storage\n                            bookData = await (0,_lib_offline_storage__WEBPACK_IMPORTED_MODULE_9__.getOfflineBook)(params.slug);\n                        } else {\n                            // Fetch from API\n                            bookData = await _lib_api__WEBPACK_IMPORTED_MODULE_21__.api.getBookById(params.slug);\n                        }\n                        setBook(bookData);\n                        // Load reading progress\n                        const progress = await (0,_lib_offline_storage__WEBPACK_IMPORTED_MODULE_9__.getReadingProgress)(params.slug);\n                        if (progress) {\n                            setReadingProgress(progress);\n                        }\n                        // Track view activity\n                        // In a real app, we would get the user ID from authentication\n                        const mockUserId = \"user-123\";\n                        (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"view\", bookData.id, \"book\", {\n                            title: bookData.title,\n                            category: ((_bookData_categories = bookData.categories) === null || _bookData_categories === void 0 ? void 0 : _bookData_categories[0]) || \"Uncategorized\"\n                        });\n                        // Start reading session\n                        setReadingStartTime(new Date());\n                        setReadingSessionId(\"reading-session-\".concat(Date.now()));\n                    } catch (err) {\n                        console.error(\"Error fetching book:\", err);\n                        setError((0,_lib_api__WEBPACK_IMPORTED_MODULE_21__.handleApiError)(err));\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"BookPage.useEffect.fetchBook\"];\n            fetchBook();\n            // Cleanup function to track reading time when component unmounts\n            return ({\n                \"BookPage.useEffect\": ()=>{\n                    if (book && readingStartTime) {\n                        const endTime = new Date();\n                        const timeSpentMinutes = Math.round((endTime.getTime() - readingStartTime.getTime()) / 60000);\n                        if (timeSpentMinutes > 0) {\n                            var _book_categories;\n                            // Track read activity with time spent\n                            const mockUserId = \"user-123\";\n                            (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"read\", book.id, \"book\", {\n                                title: book.title,\n                                category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\",\n                                timeSpent: timeSpentMinutes,\n                                progress: (readingProgress === null || readingProgress === void 0 ? void 0 : readingProgress.completionPercentage) || 0,\n                                sessionId: readingSessionId\n                            });\n                        }\n                    }\n                }\n            })[\"BookPage.useEffect\"];\n        }\n    }[\"BookPage.useEffect\"], [\n        params.slug,\n        isOffline\n    ]);\n    // Save reading progress periodically\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BookPage.useEffect\": ()=>{\n            if (!book || !contentRef.current) return;\n            const content = contentRef.current;\n            const totalLength = content.scrollHeight;\n            const saveProgress = {\n                \"BookPage.useEffect.saveProgress\": async ()=>{\n                    const position = content.scrollTop;\n                    const completionPercentage = Math.min(100, Math.round(position / (totalLength - content.clientHeight) * 100));\n                    const progress = {\n                        position,\n                        totalLength,\n                        lastReadAt: new Date().toISOString(),\n                        completionPercentage: isNaN(completionPercentage) ? 0 : completionPercentage\n                    };\n                    setReadingProgress(progress);\n                    try {\n                        await (0,_lib_offline_storage__WEBPACK_IMPORTED_MODULE_9__.saveReadingProgress)(book.id, progress);\n                        // Track progress update if significant change (every 10%)\n                        if (progress.completionPercentage % 10 === 0) {\n                            var _book_categories;\n                            const mockUserId = \"user-123\";\n                            (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"read\", book.id, \"book\", {\n                                title: book.title,\n                                category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\",\n                                progress: progress.completionPercentage,\n                                sessionId: readingSessionId\n                            });\n                            // If completed, track completion\n                            if (progress.completionPercentage >= 90) {\n                                var _book_categories1;\n                                (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"complete\", book.id, \"book\", {\n                                    title: book.title,\n                                    category: ((_book_categories1 = book.categories) === null || _book_categories1 === void 0 ? void 0 : _book_categories1[0]) || \"Uncategorized\"\n                                });\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Error saving reading progress:\", error);\n                    }\n                }\n            }[\"BookPage.useEffect.saveProgress\"];\n            // Save progress on scroll (debounced)\n            let timeout;\n            const handleScroll = {\n                \"BookPage.useEffect.handleScroll\": ()=>{\n                    clearTimeout(timeout);\n                    timeout = setTimeout(saveProgress, 500);\n                }\n            }[\"BookPage.useEffect.handleScroll\"];\n            content.addEventListener(\"scroll\", handleScroll);\n            // Restore scroll position from saved progress\n            if (readingProgress === null || readingProgress === void 0 ? void 0 : readingProgress.position) {\n                content.scrollTop = readingProgress.position;\n            }\n            // Save progress when leaving the page\n            const handleBeforeUnload = {\n                \"BookPage.useEffect.handleBeforeUnload\": ()=>{\n                    saveProgress();\n                }\n            }[\"BookPage.useEffect.handleBeforeUnload\"];\n            window.addEventListener(\"beforeunload\", handleBeforeUnload);\n            return ({\n                \"BookPage.useEffect\": ()=>{\n                    content.removeEventListener(\"scroll\", handleScroll);\n                    window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n                    clearTimeout(timeout);\n                    saveProgress();\n                }\n            })[\"BookPage.useEffect\"];\n        }\n    }[\"BookPage.useEffect\"], [\n        book,\n        readingProgress === null || readingProgress === void 0 ? void 0 : readingProgress.position,\n        readingSessionId\n    ]);\n    // Handle text selection for sharing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BookPage.useEffect\": ()=>{\n            const handleSelection = {\n                \"BookPage.useEffect.handleSelection\": ()=>{\n                    const selection = window.getSelection();\n                    if (selection && !selection.isCollapsed) {\n                        setSelectedTextForSharing(selection.toString());\n                    }\n                }\n            }[\"BookPage.useEffect.handleSelection\"];\n            document.addEventListener(\"mouseup\", handleSelection);\n            return ({\n                \"BookPage.useEffect\": ()=>{\n                    document.removeEventListener(\"mouseup\", handleSelection);\n                }\n            })[\"BookPage.useEffect\"];\n        }\n    }[\"BookPage.useEffect\"], []);\n    const fontSizeClasses = {\n        small: \"text-sm leading-6\",\n        medium: \"text-base leading-7\",\n        large: \"text-lg leading-8\"\n    };\n    const handleOfflineStatusChange = (available)=>{\n        setIsOfflineAvailable(available);\n        // Track download activity if made available offline\n        if (available && book) {\n            var _book_categories;\n            const mockUserId = \"user-123\";\n            (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"download\", book.id, \"book\", {\n                title: book.title,\n                category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\"\n            });\n        }\n    };\n    const handleTtsPlayingChange = (playing)=>{\n        setIsTtsPlaying(playing);\n    };\n    // Estimate word count for reading speed tracking\n    const getWordCount = ()=>{\n        if (!book) return 0;\n        // Strip HTML tags and count words\n        const text = book.summary.replace(/<[^>]*>/g, \"\");\n        return text.split(/\\s+/).length;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-8 md:py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/books\",\n                    className: \"inline-flex items-center gap-1 text-muted-foreground hover:text-foreground mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Back to Books\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center items-center h-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            className: \"h-8 w-8 animate-spin text-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"ml-2\",\n                            children: \"Loading book details...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n            lineNumber: 271,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !book) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-8 md:py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/books\",\n                    className: \"inline-flex items-center gap-1 text-muted-foreground hover:text-foreground mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Back to Books\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-destructive mb-4\",\n                            children: error || \"Book not found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>router.push(\"/books\"),\n                            children: \"Browse Books\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n            lineNumber: 287,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-8 md:py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/books\",\n                            className: \"inline-flex items-center gap-1 text-muted-foreground hover:text-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Back to Books\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: isOffline ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 px-2 py-1 rounded-full text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Offline Mode\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 px-2 py-1 rounded-full text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Online\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, this),\n                readingProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_reading_progress_bar__WEBPACK_IMPORTED_MODULE_11__.ReadingProgressBar, {\n                    progress: readingProgress.completionPercentage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 29\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sticky top-24 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: book.coverUrl || \"/placeholder.svg\",\n                                                alt: \"\".concat(book.title, \" book cover\"),\n                                                className: \"w-full max-w-[240px] mx-auto rounded-xl shadow-md card-themed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 space-y-4 gradient-text-overlay p-4 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: book.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: book.author\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-4 text-sm text-muted-foreground\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-primary font-semibold\",\n                                                                        children: book.rating\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        width: \"16\",\n                                                                        height: \"16\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"currentColor\",\n                                                                        stroke: \"none\",\n                                                                        className: \"text-secondary\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                            points: \"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 355,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        book.pages,\n                                                                        \" pages\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: book.language\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-4 flex flex-wrap gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_book_bookmark__WEBPACK_IMPORTED_MODULE_7__.BookBookmark, {\n                                                                bookId: book.id,\n                                                                bookTitle: book.title,\n                                                                onBookmark: ()=>{\n                                                                    var _book_categories;\n                                                                    // Track bookmark activity\n                                                                    const mockUserId = \"user-123\";\n                                                                    (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"bookmark\", book.id, \"book\", {\n                                                                        title: book.title,\n                                                                        category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\"\n                                                                    });\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_offline_book_toggle__WEBPACK_IMPORTED_MODULE_8__.OfflineBookToggle, {\n                                                                book: book,\n                                                                onStatusChange: handleOfflineStatusChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_social_sharing__WEBPACK_IMPORTED_MODULE_17__.SocialSharing, {\n                                                                contentId: book.id,\n                                                                contentTitle: book.title,\n                                                                contentType: \"book\",\n                                                                quote: selectedTextForSharing || undefined\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === \"reading\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_reading_speed_tracker__WEBPACK_IMPORTED_MODULE_16__.ReadingSpeedTracker, {\n                                        contentId: book.id,\n                                        contentLength: getWordCount()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 43\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_recommendations_similar_content__WEBPACK_IMPORTED_MODULE_19__.SimilarContent, {\n                                        contentId: book.id,\n                                        contentType: \"book\",\n                                        contentTitle: book.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                                defaultValue: \"reading\",\n                                value: activeTab,\n                                onValueChange: setActiveTab,\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"reading\",\n                                                className: \"gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Reading\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"tools\",\n                                                className: \"gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Learning Tools\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"quiz\",\n                                                className: \"gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Quiz\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"reading\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                            className: \"p-6 rounded-xl border-none shadow-sm card-themed\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: fontSize === \"small\" ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"rounded-full h-8 w-8 p-0\",\n                                                                    onClick: ()=>setFontSize(\"small\"),\n                                                                    children: \"S\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: fontSize === \"medium\" ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"rounded-full h-8 w-8 p-0\",\n                                                                    onClick: ()=>setFontSize(\"medium\"),\n                                                                    children: \"M\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: fontSize === \"large\" ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"rounded-full h-8 w-8 p-0\",\n                                                                    onClick: ()=>setFontSize(\"large\"),\n                                                                    children: \"L\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: isHighlighting ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"gap-1\",\n                                                                    onClick: ()=>setIsHighlighting(!isHighlighting),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"2\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"m9 11-6 6v3h9l3-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 461,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"m22 12-4.6 4.6a2 2 0 0 1-2.8 0l-5.2-5.2a2 2 0 0 1 0-2.8L14 4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 462,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 450,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Highlight\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 464,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: showNotes ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"gap-1\",\n                                                                    onClick: ()=>{\n                                                                        setShowNotes(!showNotes);\n                                                                        // Track note creation activity\n                                                                        if (!showNotes && book) {\n                                                                            var _book_categories;\n                                                                            const mockUserId = \"user-123\";\n                                                                            (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"note_create\", book.id, \"book\", {\n                                                                                title: book.title,\n                                                                                category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\"\n                                                                            });\n                                                                        }\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 484,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Notes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 \".concat(showNotes ? \"pr-4\" : \"\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                                                                defaultValue: \"summary\",\n                                                                className: \"w-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                                                        className: \"mb-4 bg-muted/50 p-1 rounded-full\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                                                value: \"summary\",\n                                                                                className: \"rounded-full\",\n                                                                                children: \"Summary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 494,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                                                value: \"key-insights\",\n                                                                                className: \"rounded-full\",\n                                                                                children: \"Key Insights\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 497,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                                                value: \"applications\",\n                                                                                className: \"rounded-full\",\n                                                                                children: \"Applications\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 500,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 493,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        ref: contentRef,\n                                                                        className: \"max-h-[70vh] overflow-y-auto pr-4 custom-scrollbar\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                                                                value: \"summary\",\n                                                                                className: \"\".concat(fontSizeClasses[fontSize], \" space-y-4\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    dangerouslySetInnerHTML: {\n                                                                                        __html: book.summary\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 507,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 506,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                                                                value: \"key-insights\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"\".concat(fontSizeClasses[fontSize], \" space-y-4\"),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        dangerouslySetInnerHTML: {\n                                                                                            __html: book.keyInsights || book.summary\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                        lineNumber: 512,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 511,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 510,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                                                                value: \"applications\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"\".concat(fontSizeClasses[fontSize], \" space-y-4\"),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        dangerouslySetInnerHTML: {\n                                                                                            __html: book.applications || \"\"\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                        lineNumber: 518,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 517,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 516,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 505,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showNotes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-64 border-l pl-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notes_panel__WEBPACK_IMPORTED_MODULE_13__.NotesPanel, {\n                                                                contentId: book.id\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isHighlighting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 border-t pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_highlight_controls__WEBPACK_IMPORTED_MODULE_12__.HighlightControls, {\n                                                        contentId: book.id\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 21\n                                                }, this),\n                                                !isTtsPlaying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 border-t pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_text_to_speech_player__WEBPACK_IMPORTED_MODULE_14__.TextToSpeechPlayer, {\n                                                        text: book.summary.replace(/<[^>]*>/g, \"\"),\n                                                        onPlayingChange: handleTtsPlayingChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"tools\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                    className: \"p-6 rounded-xl border-none shadow-sm card-themed\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-bold mb-4\",\n                                                            children: \"Flashcards\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flashcard_creator__WEBPACK_IMPORTED_MODULE_15__.FlashcardCreator, {\n                                                            contentId: book.id,\n                                                            contentTitle: book.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                    className: \"p-6 rounded-xl border-none shadow-sm card-themed\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-bold mb-4\",\n                                                            children: \"Text-to-Speech\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_text_to_speech_player__WEBPACK_IMPORTED_MODULE_14__.TextToSpeechPlayer, {\n                                                            text: book.summary.replace(/<[^>]*>/g, \"\"),\n                                                            onPlayingChange: handleTtsPlayingChange\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"quiz\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                            className: \"p-6 rounded-xl border-none shadow-sm card-themed\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold mb-4\",\n                                                    children: \"Test Your Understanding\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_comprehension_quiz__WEBPACK_IMPORTED_MODULE_18__.ComprehensionQuiz, {\n                                                    contentId: book.id,\n                                                    contentTitle: book.title,\n                                                    contentType: \"book\",\n                                                    onQuizAttempt: ()=>{\n                                                        var _book_categories;\n                                                        // Track quiz attempt activity\n                                                        const mockUserId = \"user-123\";\n                                                        (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"quiz_attempt\", book.id, \"book\", {\n                                                            title: book.title,\n                                                            category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\"\n                                                        });\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n            lineNumber: 305,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n        lineNumber: 304,\n        columnNumber: 5\n    }, this);\n}\n_s(BookPage, \"yR5tjC9fYAccchtyl4fak/7BNFU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = BookPage;\nvar _c;\n$RefreshReg$(_c, \"BookPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/books/[slug]/page.tsx\n"));

/***/ })

});