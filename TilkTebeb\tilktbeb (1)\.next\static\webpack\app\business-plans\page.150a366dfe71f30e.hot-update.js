"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/business-plans/page",{

/***/ "(app-pages-browser)/./app/business-plans/page.tsx":
/*!*************************************!*\
  !*** ./app/business-plans/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BusinessPlansPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction BusinessPlansPage() {\n    _s();\n    const [businessPlans, setBusinessPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeSize, setActiveSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"small\");\n    // Fetch business plans from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BusinessPlansPage.useEffect\": ()=>{\n            const fetchBusinessPlans = {\n                \"BusinessPlansPage.useEffect.fetchBusinessPlans\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.api.getBusinessPlans();\n                        setBusinessPlans(data);\n                    } catch (err) {\n                        setError((0,_lib_api__WEBPACK_IMPORTED_MODULE_6__.handleApiError)(err));\n                        console.error(\"Error fetching business plans:\", err);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"BusinessPlansPage.useEffect.fetchBusinessPlans\"];\n            fetchBusinessPlans();\n        }\n    }[\"BusinessPlansPage.useEffect\"], []);\n    // Filter plans by size\n    const filteredPlans = businessPlans.filter((plan)=>plan.size === activeSize);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container py-8 md:py-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold tracking-tight mb-2\",\n                            children: \"Business Plans\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Detailed business plans for different business sizes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                defaultValue: \"small\",\n                value: activeSize,\n                onValueChange: (value)=>setActiveSize(value),\n                className: \"mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"small\",\n                                children: \"Small Business\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"medium\",\n                                children: \"Medium Business\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"large\",\n                                children: \"Large Business\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"small\",\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                ...Array(6)\n                            ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-40 bg-muted rounded-lg mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-5 bg-muted rounded w-3/4 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-full mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-5/6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-destructive mb-4\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>window.location.reload(),\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: filteredPlans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                children: plan.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                                lineNumber: 87,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                                children: plan.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                                lineNumber: 88,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    plan.isPremium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-primary/10 text-primary text-xs px-2 py-1 rounded-full flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Premium\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-5 w-5 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Small Business\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: plan.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/business-plans/\".concat(plan.size, \"/\").concat(plan.id),\n                                                className: \"w-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: plan.isPremium ? \"outline\" : \"default\",\n                                                    className: \"w-full\",\n                                                    children: plan.isPremium ? \"Unlock Plan\" : \"View Plan\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, plan.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"medium\",\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                ...Array(6)\n                            ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-40 bg-muted rounded-lg mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-5 bg-muted rounded w-3/4 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-full mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-5/6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-destructive mb-4\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>window.location.reload(),\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: filteredPlans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                children: plan.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                                lineNumber: 145,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                                children: plan.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                                lineNumber: 146,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    plan.isPremium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-primary/10 text-primary text-xs px-2 py-1 rounded-full flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Premium\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-5 w-5 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Medium Business\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: plan.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/business-plans/\".concat(plan.size, \"/\").concat(plan.id),\n                                                className: \"w-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full\",\n                                                    children: \"Unlock Plan\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, plan.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"large\",\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                ...Array(6)\n                            ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-40 bg-muted rounded-lg mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-5 bg-muted rounded w-3/4 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-full mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-5/6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-destructive mb-4\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>window.location.reload(),\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: filteredPlans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                children: plan.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                                children: plan.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    plan.isPremium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-primary/10 text-primary text-xs px-2 py-1 rounded-full flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Premium\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-5 w-5 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Large Business\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: plan.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/business-plans/\".concat(plan.size, \"/\").concat(plan.id),\n                                                className: \"w-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full\",\n                                                    children: \"Unlock Plan\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, plan.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-primary/5 rounded-lg p-6 md:p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold mb-4\",\n                                    children: \"Unlock All Premium Business Plans\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground mb-6\",\n                                    children: \"Get lifetime access to all our premium business plans with a one-time payment. Choose the business size that fits your needs or get access to all plans.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/pricing\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                size: \"lg\",\n                                                children: \"View Pricing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/signup\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                children: \"Sign Up Now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-background rounded-md p-4 text-center shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: \"Small\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold mb-1\",\n                                            children: \"$149\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"one-time payment\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-background rounded-md p-4 text-center shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: \"Medium\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold mb-1\",\n                                            children: \"$249\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"one-time payment\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-background rounded-md p-4 text-center shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: \"Large\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold mb-1\",\n                                            children: \"$399\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"one-time payment\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n_s(BusinessPlansPage, \"fDiGrt6+D5gl6IG0i4tVlI/URJ0=\");\n_c = BusinessPlansPage;\nvar _c;\n$RefreshReg$(_c, \"BusinessPlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/business-plans/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api-service.ts":
/*!****************************!*\
  !*** ./lib/api-service.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiService: () => (/* binding */ ApiService)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Configuration for Django API\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';\n// API service class for handling external Django API calls\nclass ApiService {\n    // Generic fetch wrapper with error handling\n    static async fetchWithErrorHandling(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseUrl).concat(endpoint);\n        try {\n            const response = await fetch(url, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    ...options.headers\n                },\n                ...options\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            return await response.json();\n        } catch (error) {\n            console.error(\"API call failed for \".concat(endpoint, \":\"), error);\n            throw error;\n        }\n    }\n    // Books API methods\n    static async getBooks(params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.category) searchParams.append('category', params.category);\n        if (params === null || params === void 0 ? void 0 : params.query) searchParams.append('query', params.query);\n        if ((params === null || params === void 0 ? void 0 : params.premium) !== undefined) searchParams.append('premium', params.premium.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        const endpoint = \"/books/\".concat(searchParams.toString() ? \"?\".concat(searchParams.toString()) : '');\n        return this.fetchWithErrorHandling(endpoint);\n    }\n    static async getBookById(id) {\n        return this.fetchWithErrorHandling(\"/books/\".concat(id, \"/\"));\n    }\n    // Business Plans API methods\n    static async getBusinessPlans(params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.size) searchParams.append('size', params.size);\n        if (params === null || params === void 0 ? void 0 : params.category) searchParams.append('category', params.category);\n        if (params === null || params === void 0 ? void 0 : params.query) searchParams.append('query', params.query);\n        if ((params === null || params === void 0 ? void 0 : params.premium) !== undefined) searchParams.append('premium', params.premium.toString());\n        const endpoint = \"/business-plans/\".concat(searchParams.toString() ? \"?\".concat(searchParams.toString()) : '');\n        return this.fetchWithErrorHandling(endpoint);\n    }\n    static async getBusinessPlanById(id) {\n        return this.fetchWithErrorHandling(\"/business-plans/\".concat(id, \"/\"));\n    }\n    // Authentication API methods\n    static async login(credentials) {\n        return this.fetchWithErrorHandling('/auth/login/', {\n            method: 'POST',\n            body: JSON.stringify(credentials)\n        });\n    }\n    static async register(userData) {\n        return this.fetchWithErrorHandling('/auth/register/', {\n            method: 'POST',\n            body: JSON.stringify(userData)\n        });\n    }\n    // User API methods\n    static async getUserProfile(userId) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/\"));\n    }\n    static async updateUserProfile(userId, userData) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/\"), {\n            method: 'PATCH',\n            body: JSON.stringify(userData)\n        });\n    }\n    // Payment API methods\n    static async processPayment(paymentData) {\n        return this.fetchWithErrorHandling('/payments/', {\n            method: 'POST',\n            body: JSON.stringify(paymentData)\n        });\n    }\n    static async verifyPayment(transactionId) {\n        return this.fetchWithErrorHandling(\"/payments/verify/\".concat(transactionId, \"/\"));\n    }\n    static async getPaymentHistory(userId) {\n        return this.fetchWithErrorHandling(\"/payments/?userId=\".concat(userId));\n    }\n    // Admin API methods (for admin dashboard)\n    static async getAdminStats() {\n        return this.fetchWithErrorHandling('/admin/stats/');\n    }\n    static async getAdminUsers(params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        const endpoint = \"/admin/users/\".concat(searchParams.toString() ? \"?\".concat(searchParams.toString()) : '');\n        return this.fetchWithErrorHandling(endpoint);\n    }\n    // Bookmarks API methods\n    static async getUserBookmarks(userId) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/bookmarks/\"));\n    }\n    static async addBookmark(userId, bookId) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/bookmarks/\"), {\n            method: 'POST',\n            body: JSON.stringify({\n                bookId\n            })\n        });\n    }\n    static async removeBookmark(userId, bookId) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/bookmarks/\").concat(bookId, \"/\"), {\n            method: 'DELETE'\n        });\n    }\n    // Analytics API methods\n    static async getUserAnalytics(userId) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/analytics/\"));\n    }\n}\nApiService.baseUrl = API_BASE_URL;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api-service.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: () => (/* binding */ API_CONFIG),\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   authTokenManager: () => (/* binding */ authTokenManager),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   isUsingMockApi: () => (/* binding */ isUsingMockApi),\n/* harmony export */   useApiCall: () => (/* binding */ useApiCall),\n/* harmony export */   userSessionManager: () => (/* binding */ userSessionManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _api_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api-service */ \"(app-pages-browser)/./lib/api-service.ts\");\n/* harmony import */ var _mock_api_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mock-api-service */ \"(app-pages-browser)/./lib/mock-api-service.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n\n\n// Environment configuration\nconst USE_MOCK_API = process.env.NEXT_PUBLIC_USE_MOCK_API === 'true' || \"development\" === 'development';\n// Export the appropriate service based on environment\nconst api = USE_MOCK_API ? _mock_api_service__WEBPACK_IMPORTED_MODULE_2__.MockApiService : _api_service__WEBPACK_IMPORTED_MODULE_1__.ApiService;\n// Utility function to check if we're using mock API\nconst isUsingMockApi = ()=>USE_MOCK_API;\n// Helper function for error handling in components\nconst handleApiError = (error)=>{\n    if (error instanceof Error) {\n        return error.message;\n    }\n    return 'An unexpected error occurred';\n};\n// Configuration constants\nconst API_CONFIG = {\n    BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api',\n    TIMEOUT: 10000,\n    RETRY_ATTEMPTS: 3,\n    USE_MOCK: USE_MOCK_API\n};\n// Authentication token management\nconst authTokenManager = {\n    getToken: ()=>{\n        if (false) {}\n        return localStorage.getItem('auth_token');\n    },\n    setToken: (token)=>{\n        if (false) {}\n        localStorage.setItem('auth_token', token);\n    },\n    removeToken: ()=>{\n        if (false) {}\n        localStorage.removeItem('auth_token');\n    },\n    isAuthenticated: ()=>{\n        return !!authTokenManager.getToken();\n    }\n};\n// User session management\nconst userSessionManager = {\n    getCurrentUser: ()=>{\n        if (false) {}\n        const userStr = localStorage.getItem('current_user');\n        return userStr ? JSON.parse(userStr) : null;\n    },\n    setCurrentUser: (user)=>{\n        if (false) {}\n        localStorage.setItem('current_user', JSON.stringify(user));\n    },\n    removeCurrentUser: ()=>{\n        if (false) {}\n        localStorage.removeItem('current_user');\n    },\n    logout: ()=>{\n        authTokenManager.removeToken();\n        userSessionManager.removeCurrentUser();\n    }\n};\n// Custom hook for API calls with loading and error states\nconst useApiCall = function(apiCall) {\n    let dependencies = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await apiCall();\n            setData(result);\n        } catch (err) {\n            setError(handleApiError(err));\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useApiCall.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"useApiCall.useEffect\"], dependencies);\n    return {\n        data,\n        loading,\n        error,\n        refetch: fetchData\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/mock-api-service.ts":
/*!*********************************!*\
  !*** ./lib/mock-api-service.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MockApiService: () => (/* binding */ MockApiService)\n/* harmony export */ });\n/* harmony import */ var _mock_data__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mock-data */ \"(app-pages-browser)/./lib/mock-data.ts\");\n\n// Mock API service for development when Django backend is not available\nclass MockApiService {\n    // Simulate network delay\n    static delay() {\n        let ms = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 500;\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    // Books API methods\n    static async getBooks(params) {\n        await this.delay();\n        let filteredBooks = [\n            ..._mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBookPreviews\n        ];\n        if ((params === null || params === void 0 ? void 0 : params.category) && params.category !== \"all\") {\n            filteredBooks = filteredBooks.filter((book)=>book.category.toLowerCase() === params.category.toLowerCase());\n        }\n        if (params === null || params === void 0 ? void 0 : params.query) {\n            const searchQuery = params.query.toLowerCase();\n            filteredBooks = filteredBooks.filter((book)=>book.title.toLowerCase().includes(searchQuery) || book.author.toLowerCase().includes(searchQuery));\n        }\n        if ((params === null || params === void 0 ? void 0 : params.premium) !== undefined) {\n            const fullBooks = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBooks.filter((book)=>book.isPremium === params.premium);\n            const premiumBookIds = fullBooks.map((book)=>book.id);\n            filteredBooks = filteredBooks.filter((book)=>premiumBookIds.includes(book.id));\n        }\n        if (params === null || params === void 0 ? void 0 : params.limit) {\n            filteredBooks = filteredBooks.slice(0, params.limit);\n        }\n        return filteredBooks;\n    }\n    static async getBookById(id) {\n        await this.delay();\n        const book = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBooks.find((book)=>book.id === id);\n        if (!book) {\n            throw new Error(\"Book with id \".concat(id, \" not found\"));\n        }\n        return book;\n    }\n    // Business Plans API methods\n    static async getBusinessPlans(params) {\n        await this.delay();\n        let filteredPlans = [\n            ..._mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBusinessPlanPreviews\n        ];\n        if ((params === null || params === void 0 ? void 0 : params.size) && params.size !== \"all\") {\n            filteredPlans = filteredPlans.filter((plan)=>plan.size === params.size);\n        }\n        if ((params === null || params === void 0 ? void 0 : params.category) && params.category !== \"all\") {\n            filteredPlans = filteredPlans.filter((plan)=>plan.category.toLowerCase() === params.category.toLowerCase());\n        }\n        if (params === null || params === void 0 ? void 0 : params.query) {\n            const searchQuery = params.query.toLowerCase();\n            filteredPlans = filteredPlans.filter((plan)=>plan.title.toLowerCase().includes(searchQuery) || plan.category.toLowerCase().includes(searchQuery));\n        }\n        if ((params === null || params === void 0 ? void 0 : params.premium) !== undefined) {\n            filteredPlans = filteredPlans.filter((plan)=>plan.isPremium === params.premium);\n        }\n        return filteredPlans;\n    }\n    static async getBusinessPlanById(id) {\n        await this.delay();\n        const plan = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBusinessPlans.find((plan)=>plan.id === id);\n        if (!plan) {\n            throw new Error(\"Business plan with id \".concat(id, \" not found\"));\n        }\n        return plan;\n    }\n    // Authentication API methods\n    static async login(credentials) {\n        await this.delay();\n        // Mock successful login for demo purposes\n        return {\n            id: \"user-1\",\n            firstName: \"John\",\n            lastName: \"Doe\",\n            email: credentials.email,\n            plan: \"medium\",\n            token: \"mock-jwt-token-123\"\n        };\n    }\n    static async register(userData) {\n        await this.delay();\n        // Mock successful registration\n        return {\n            id: \"user-\".concat(Date.now()),\n            firstName: userData.firstName,\n            lastName: userData.lastName,\n            email: userData.email,\n            plan: \"base\",\n            token: \"mock-jwt-token-456\"\n        };\n    }\n    // User API methods\n    static async getUserProfile(userId) {\n        await this.delay();\n        return {\n            user: {\n                id: userId,\n                firstName: \"John\",\n                lastName: \"Doe\",\n                email: \"<EMAIL>\",\n                plan: \"medium\"\n            },\n            activities: [\n                {\n                    id: \"activity-1\",\n                    userId: userId,\n                    type: \"book_read\",\n                    itemId: \"the-psychology-of-money\",\n                    itemTitle: \"The Psychology of Money\",\n                    timestamp: new Date(Date.now() - 86400000).toISOString()\n                },\n                {\n                    id: \"activity-2\",\n                    userId: userId,\n                    type: \"plan_viewed\",\n                    itemId: \"small-1\",\n                    itemTitle: \"Local Coffee Shop\",\n                    timestamp: new Date(Date.now() - 172800000).toISOString()\n                }\n            ]\n        };\n    }\n    static async updateUserProfile(userId, userData) {\n        await this.delay();\n        return {\n            id: userId,\n            firstName: userData.firstName || \"John\",\n            lastName: userData.lastName || \"Doe\",\n            email: userData.email || \"<EMAIL>\",\n            plan: \"medium\"\n        };\n    }\n    // Payment API methods\n    static async processPayment(paymentData) {\n        await this.delay(1000) // Longer delay for payment processing\n        ;\n        // Mock successful payment\n        return {\n            success: true,\n            transactionId: \"TRX-\".concat(Date.now(), \"-\").concat(Math.floor(Math.random() * 1000)),\n            message: \"Payment processed successfully\",\n            timestamp: new Date().toISOString(),\n            plan: paymentData.plan\n        };\n    }\n    static async verifyPayment(transactionId) {\n        await this.delay();\n        return {\n            success: true,\n            status: \"completed\",\n            message: \"Payment verified successfully\"\n        };\n    }\n    static async getPaymentHistory(userId) {\n        await this.delay();\n        return [\n            {\n                success: true,\n                transactionId: \"TRX-123456789\",\n                message: \"Payment processed successfully\",\n                timestamp: new Date(Date.now() - 2592000000).toISOString(),\n                plan: \"medium\"\n            }\n        ];\n    }\n    // Admin API methods\n    static async getAdminStats() {\n        await this.delay();\n        return {\n            totalUsers: 1250,\n            totalBooks: _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBooks.length,\n            totalBusinessPlans: _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBusinessPlans.length,\n            revenueThisMonth: 15750\n        };\n    }\n    static async getAdminUsers(params) {\n        await this.delay();\n        const mockUsers = [\n            {\n                id: \"user-1\",\n                firstName: \"John\",\n                lastName: \"Doe\",\n                email: \"<EMAIL>\",\n                plan: \"medium\",\n                createdAt: \"2023-01-15T00:00:00Z\"\n            },\n            {\n                id: \"user-2\",\n                firstName: \"Jane\",\n                lastName: \"Smith\",\n                email: \"<EMAIL>\",\n                plan: \"base\",\n                createdAt: \"2023-03-22T00:00:00Z\"\n            }\n        ];\n        return {\n            users: mockUsers,\n            total: mockUsers.length,\n            page: (params === null || params === void 0 ? void 0 : params.page) || 1,\n            totalPages: 1\n        };\n    }\n    // Bookmarks API methods\n    static async getUserBookmarks(userId) {\n        await this.delay();\n        // Return first 2 books as bookmarked for demo\n        return _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBookPreviews.slice(0, 2);\n    }\n    static async addBookmark(userId, bookId) {\n        await this.delay();\n        return {\n            success: true\n        };\n    }\n    static async removeBookmark(userId, bookId) {\n        await this.delay();\n        return {\n            success: true\n        };\n    }\n    // Analytics API methods\n    static async getUserAnalytics(userId) {\n        await this.delay();\n        return {\n            readingStats: {\n                booksRead: 12,\n                totalReadingTime: 2400,\n                averageReadingSpeed: 250,\n                streakDays: 7\n            },\n            recentActivity: [\n                {\n                    id: \"activity-1\",\n                    type: \"book_read\",\n                    itemTitle: \"The Psychology of Money\",\n                    timestamp: new Date(Date.now() - 86400000).toISOString()\n                },\n                {\n                    id: \"activity-2\",\n                    type: \"plan_viewed\",\n                    itemTitle: \"Local Coffee Shop\",\n                    timestamp: new Date(Date.now() - 172800000).toISOString()\n                }\n            ]\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/mock-api-service.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/mock-data.ts":
/*!**************************!*\
  !*** ./lib/mock-data.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mockBookPreviews: () => (/* binding */ mockBookPreviews),\n/* harmony export */   mockBooks: () => (/* binding */ mockBooks),\n/* harmony export */   mockBusinessPlanPreviews: () => (/* binding */ mockBusinessPlanPreviews),\n/* harmony export */   mockBusinessPlans: () => (/* binding */ mockBusinessPlans)\n/* harmony export */ });\n// Mock data for books - this will be replaced by Django API calls\nconst mockBooks = [\n    {\n        id: \"the-psychology-of-money\",\n        title: \"The Psychology Of Money\",\n        author: \"Morgan Housel\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Finance\",\n        rating: 4.4,\n        pages: 242,\n        language: \"English\",\n        summary: \"\\n      <p>The Psychology of Money explores how money moves around in an economy and how people behave with it. The author, Morgan Housel, provides timeless lessons on wealth, greed, and happiness.</p>\\n      \\n      <h3>Key Insights:</h3>\\n      \\n      <p><strong>1. Financial success is not a hard science</strong></p>\\n      <p>It's a soft skill where how you behave is more important than what you know. Financial outcomes are driven by luck, independent of intelligence and effort.</p>\\n      \\n      <p><strong>2. No one is crazy with money</strong></p>\\n      <p>People make financial decisions based on their unique experiences, their own personal history, unique worldview, ego, pride, marketing, and odd incentives.</p>\\n      \\n      <p><strong>3. Luck and risk are siblings</strong></p>\\n      <p>They are both the reality that every outcome in life is guided by forces other than individual effort. They both happen because the world is too complex to allow 100% of your actions to dictate 100% of your outcomes.</p>\\n      \\n      <p><strong>4. Never enough</strong></p>\\n      <p>When rich people do crazy things, it's often a case of trying to feel valued after already having more money than they know what to do with.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Financial success is not a hard science</strong></p>\\n      <p>It's a soft skill where how you behave is more important than what you know. Financial outcomes are driven by luck, independent of intelligence and effort.</p>\\n      \\n      <p><strong>2. No one is crazy with money</strong></p>\\n      <p>People make financial decisions based on their unique experiences, their own personal history, unique worldview, ego, pride, marketing, and odd incentives.</p>\\n      \\n      <p><strong>3. Luck and risk are siblings</strong></p>\\n      <p>They are both the reality that every outcome in life is guided by forces other than individual effort. They both happen because the world is too complex to allow 100% of your actions to dictate 100% of your outcomes.</p>\\n      \\n      <p><strong>4. Never enough</strong></p>\\n      <p>When rich people do crazy things, it's often a case of trying to feel valued after already having more money than they know what to do with.</p>\\n    \",\n        applications: \"\\n      <p><strong>For Personal Finance:</strong></p>\\n      <ul>\\n        <li>Save money without a specific goal in mind</li>\\n        <li>Gain control over your time</li>\\n        <li>Be reasonable rather than rational</li>\\n        <li>Aim for enough, not for maximum</li>\\n      </ul>\\n      \\n      <p><strong>For Investors:</strong></p>\\n      <ul>\\n        <li>Understand the role of luck and risk</li>\\n        <li>Know that getting wealthy and staying wealthy are different skills</li>\\n        <li>Long tails drive everything - a small number of events can account for the majority of outcomes</li>\\n        <li>Use room for error when investing - prepare for a range of outcomes</li>\\n      </ul>\\n    \",\n        isPremium: false\n    },\n    {\n        id: \"atomic-habits\",\n        title: \"Atomic Habits\",\n        author: \"James Clear\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Productivity\",\n        rating: 4.9,\n        pages: 320,\n        language: \"English\",\n        summary: \"\\n      <p>Atomic Habits offers a proven framework for improving every day. James Clear reveals practical strategies that will teach you exactly how to form good habits, break bad ones, and master the tiny behaviors that lead to remarkable results.</p>\\n      \\n      <h3>Key Insights:</h3>\\n      \\n      <p><strong>1. Habits are the compound interest of self-improvement</strong></p>\\n      <p>Small changes often appear to make no difference until you cross a critical threshold. The effects of your habits multiply as you repeat them.</p>\\n      \\n      <p><strong>2. Focus on systems instead of goals</strong></p>\\n      <p>Goals are about the results you want to achieve. Systems are about the processes that lead to those results. Focus on the system, not the goal.</p>\\n      \\n      <p><strong>3. The Four Laws of Behavior Change</strong></p>\\n      <p>Make it obvious, make it attractive, make it easy, and make it satisfying. These are the fundamental principles behind habit formation.</p>\\n      \\n      <p><strong>4. Identity-based habits</strong></p>\\n      <p>The most effective way to change your habits is to focus not on what you want to achieve, but on who you wish to become.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Habits are the compound interest of self-improvement</strong></p>\\n      <p>Small changes often appear to make no difference until you cross a critical threshold. The effects of your habits multiply as you repeat them.</p>\\n      \\n      <p><strong>2. Focus on systems instead of goals</strong></p>\\n      <p>Goals are about the results you want to achieve. Systems are about the processes that lead to those results. Focus on the system, not the goal.</p>\\n      \\n      <p><strong>3. The Four Laws of Behavior Change</strong></p>\\n      <p>Make it obvious, make it attractive, make it easy, and make it satisfying. These are the fundamental principles behind habit formation.</p>\\n      \\n      <p><strong>4. Identity-based habits</strong></p>\\n      <p>The most effective way to change your habits is to focus not on what you want to achieve, but on who you wish to become.</p>\\n    \",\n        applications: \"\\n      <p><strong>For Personal Development:</strong></p>\\n      <ul>\\n        <li>Start with an incredibly small habit</li>\\n        <li>Increase your habit in very small ways</li>\\n        <li>Break habits into chunks</li>\\n        <li>When you slip, get back on track quickly</li>\\n      </ul>\\n      \\n      <p><strong>For Business:</strong></p>\\n      <ul>\\n        <li>Create an environment where doing the right thing is as easy as possible</li>\\n        <li>Make good habits obvious in your environment</li>\\n        <li>Reduce friction for good habits</li>\\n        <li>Increase friction for bad habits</li>\\n      </ul>\\n    \",\n        isPremium: false\n    },\n    {\n        id: \"sapiens\",\n        title: \"Sapiens\",\n        author: \"Yuval Noah Harari\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"History\",\n        rating: 4.7,\n        pages: 464,\n        language: \"English\",\n        summary: '\\n      <p>Sapiens: A Brief History of Humankind is a book by Yuval Noah Harari that explores the history and impact of Homo sapiens on the world. It traces the evolution of our species from the emergence of Homo sapiens in Africa to our current status as the dominant force on Earth.</p>\\n      \\n      <h3>Key Insights:</h3>\\n      \\n      <p><strong>1. The Cognitive Revolution</strong></p>\\n      <p>Around 70,000 years ago, Homo sapiens developed unique cognitive abilities, particularly the capacity for fiction and imagination, which allowed for unprecedented cooperation among large groups.</p>\\n      \\n      <p><strong>2. The Agricultural Revolution</strong></p>\\n      <p>Beginning about 12,000 years ago, humans transitioned from hunting and gathering to farming, which Harari describes as \"history\\'s biggest fraud\" because it led to harder work, less leisure, and poorer health for most people.</p>\\n      \\n      <p><strong>3. The Unification of Humankind</strong></p>\\n      <p>Over time, humans have created increasingly larger networks of cooperation through shared myths, including money, empires, and religions.</p>\\n      \\n      <p><strong>4. The Scientific Revolution</strong></p>\\n      <p>The last 500 years have seen an explosion of human power through the willingness to admit ignorance and the development of the scientific method.</p>\\n    ',\n        keyInsights: '\\n      <p><strong>1. The Cognitive Revolution</strong></p>\\n      <p>Around 70,000 years ago, Homo sapiens developed unique cognitive abilities, particularly the capacity for fiction and imagination, which allowed for unprecedented cooperation among large groups.</p>\\n      \\n      <p><strong>2. The Agricultural Revolution</strong></p>\\n      <p>Beginning about 12,000 years ago, humans transitioned from hunting and gathering to farming, which Harari describes as \"history\\'s biggest fraud\" because it led to harder work, less leisure, and poorer health for most people.</p>\\n      \\n      <p><strong>3. The Unification of Humankind</strong></p>\\n      <p>Over time, humans have created increasingly larger networks of cooperation through shared myths, including money, empires, and religions.</p>\\n      \\n      <p><strong>4. The Scientific Revolution</strong></p>\\n      <p>The last 500 years have seen an explosion of human power through the willingness to admit ignorance and the development of the scientific method.</p>\\n    ',\n        applications: '\\n      <p><strong>For Understanding Society:</strong></p>\\n      <ul>\\n        <li>Recognize how shared myths and stories shape our world</li>\\n        <li>Understand the historical context of current social structures</li>\\n        <li>Question whether \"progress\" always means improvement</li>\\n        <li>Consider the ethical implications of technological advancement</li>\\n      </ul>\\n      \\n      <p><strong>For Business and Leadership:</strong></p>\\n      <ul>\\n        <li>Appreciate how shared narratives create cohesion in organizations</li>\\n        <li>Understand how money and corporations are social constructs that depend on trust</li>\\n        <li>Consider the long-term implications of short-term decisions</li>\\n        <li>Recognize patterns of human behavior that persist across time</li>\\n      </ul>\\n    ',\n        isPremium: true\n    },\n    {\n        id: \"zero-to-one\",\n        title: \"Zero to One\",\n        author: \"Peter Thiel\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Entrepreneurship\",\n        rating: 4.8,\n        pages: 224,\n        language: \"English\",\n        summary: \"\\n      <p>Zero to One presents at once an optimistic view of the future of progress in America and a new way of thinking about innovation: it starts by learning to ask the questions that lead you to find value in unexpected places.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Vertical vs. Horizontal Progress</strong></p>\\n      <p>Horizontal progress means copying things that work—going from 1 to n. Vertical progress means doing new things—going from 0 to 1. The focus should be on creating something new rather than copying existing models.</p>\\n\\n      <p><strong>2. Monopolies vs. Competition</strong></p>\\n      <p>Competition is for losers. Under perfect competition, no company makes economic profit. The goal should be to create a monopoly through innovation.</p>\\n\\n      <p><strong>3. The Power Law</strong></p>\\n      <p>A small number of companies radically outperform all others. This principle applies to venture capital investments, where a single investment may return more than all others combined.</p>\\n\\n      <p><strong>4. Secrets</strong></p>\\n      <p>Every great business is built around a secret that's hidden from the outside. Great companies find value in unexpected places by thinking about business from first principles.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Vertical vs. Horizontal Progress</strong></p>\\n      <p>Horizontal progress means copying things that work—going from 1 to n. Vertical progress means doing new things—going from 0 to 1. The focus should be on creating something new rather than copying existing models.</p>\\n\\n      <p><strong>2. Monopolies vs. Competition</strong></p>\\n      <p>Competition is for losers. Under perfect competition, no company makes economic profit. The goal should be to create a monopoly through innovation.</p>\\n\\n      <p><strong>3. The Power Law</strong></p>\\n      <p>A small number of companies radically outperform all others. This principle applies to venture capital investments, where a single investment may return more than all others combined.</p>\\n\\n      <p><strong>4. Secrets</strong></p>\\n      <p>Every great business is built around a secret that's hidden from the outside. Great companies find value in unexpected places by thinking about business from first principles.</p>\\n    \",\n        applications: '\\n      <p><strong>For Entrepreneurs:</strong></p>\\n      <ul>\\n        <li>Focus on creating something new rather than improving existing products</li>\\n        <li>Aim to create a monopoly through unique technology, network effects, economies of scale, and branding</li>\\n        <li>Start small and monopolize a niche market before expanding</li>\\n        <li>Build a great team with a strong, unified vision</li>\\n      </ul>\\n\\n      <p><strong>For Investors:</strong></p>\\n      <ul>\\n        <li>Understand that returns follow a power law—a few investments will outperform all others</li>\\n        <li>Look for companies with proprietary technology, network effects, economies of scale, and strong branding</li>\\n        <li>Evaluate the founding team\\'s dynamics and vision</li>\\n        <li>Consider whether the company has discovered a unique \"secret\" about the market</li>\\n      </ul>\\n    ',\n        isPremium: true\n    },\n    {\n        id: \"good-to-great\",\n        title: \"Good to Great\",\n        author: \"Jim Collins\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Leadership\",\n        rating: 4.7,\n        pages: 320,\n        language: \"English\",\n        summary: \"\\n      <p>Good to Great presents the findings of a five-year study by Jim Collins and his research team. The team identified a set of companies that made the leap from good results to great results and sustained those results for at least fifteen years.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Level 5 Leadership</strong></p>\\n      <p>Leaders who have a unique blend of personal humility and professional will. They are ambitious for the company, not themselves.</p>\\n\\n      <p><strong>2. First Who, Then What</strong></p>\\n      <p>Get the right people on the bus, the wrong people off the bus, and the right people in the right seats—then figure out where to drive it.</p>\\n\\n      <p><strong>3. Confront the Brutal Facts</strong></p>\\n      <p>Create a culture where people have the opportunity to be heard and where the truth is heard. Maintain unwavering faith that you can and will prevail, regardless of difficulties.</p>\\n\\n      <p><strong>4. The Hedgehog Concept</strong></p>\\n      <p>Focus on the intersection of three circles: what you can be the best in the world at, what drives your economic engine, and what you are deeply passionate about.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Level 5 Leadership</strong></p>\\n      <p>Leaders who have a unique blend of personal humility and professional will. They are ambitious for the company, not themselves.</p>\\n\\n      <p><strong>2. First Who, Then What</strong></p>\\n      <p>Get the right people on the bus, the wrong people off the bus, and the right people in the right seats—then figure out where to drive it.</p>\\n\\n      <p><strong>3. Confront the Brutal Facts</strong></p>\\n      <p>Create a culture where people have the opportunity to be heard and where the truth is heard. Maintain unwavering faith that you can and will prevail, regardless of difficulties.</p>\\n\\n      <p><strong>4. The Hedgehog Concept</strong></p>\\n      <p>Focus on the intersection of three circles: what you can be the best in the world at, what drives your economic engine, and what you are deeply passionate about.</p>\\n    \",\n        applications: \"\\n      <p><strong>For Business Leaders:</strong></p>\\n      <ul>\\n        <li>Develop Level 5 Leadership qualities: ambition for the company over self</li>\\n        <li>Focus on getting the right team in place before determining strategy</li>\\n        <li>Create a culture of disciplined people, thought, and action</li>\\n        <li>Apply the Hedgehog Concept to focus resources and efforts</li>\\n      </ul>\\n\\n      <p><strong>For Organizations:</strong></p>\\n      <ul>\\n        <li>Use technology as an accelerator, not a creator of momentum</li>\\n        <li>Build momentum gradually until breakthrough occurs (the flywheel effect)</li>\\n        <li>Maintain discipline to stick with what you can be best at</li>\\n        <li>Confront reality while maintaining faith in ultimate success</li>\\n      </ul>\\n    \",\n        isPremium: true\n    },\n    {\n        id: \"the-lean-startup\",\n        title: \"The Lean Startup\",\n        author: \"Eric Ries\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Startup\",\n        rating: 4.6,\n        pages: 336,\n        language: \"English\",\n        summary: \"\\n      <p>The Lean Startup introduces a methodology for developing businesses and products that aims to shorten product development cycles and rapidly discover if a proposed business model is viable.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Build-Measure-Learn</strong></p>\\n      <p>The fundamental activity of a startup is to turn ideas into products, measure how customers respond, and then learn whether to pivot or persevere.</p>\\n\\n      <p><strong>2. Minimum Viable Product (MVP)</strong></p>\\n      <p>The version of a new product that allows a team to collect the maximum amount of validated learning about customers with the least effort.</p>\\n\\n      <p><strong>3. Validated Learning</strong></p>\\n      <p>The process of demonstrating empirically that a team has discovered valuable truths about a startup's present and future business prospects.</p>\\n\\n      <p><strong>4. Innovation Accounting</strong></p>\\n      <p>A quantitative approach that allows startups to prove objectively that they are learning how to grow a sustainable business.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Build-Measure-Learn</strong></p>\\n      <p>The fundamental activity of a startup is to turn ideas into products, measure how customers respond, and then learn whether to pivot or persevere.</p>\\n\\n      <p><strong>2. Minimum Viable Product (MVP)</strong></p>\\n      <p>The version of a new product that allows a team to collect the maximum amount of validated learning about customers with the least effort.</p>\\n\\n      <p><strong>3. Validated Learning</strong></p>\\n      <p>The process of demonstrating empirically that a team has discovered valuable truths about a startup's present and future business prospects.</p>\\n\\n      <p><strong>4. Innovation Accounting</strong></p>\\n      <p>A quantitative approach that allows startups to prove objectively that they are learning how to grow a sustainable business.</p>\\n    \",\n        applications: \"\\n      <p><strong>For Entrepreneurs:</strong></p>\\n      <ul>\\n        <li>Start with a minimum viable product to test assumptions quickly</li>\\n        <li>Use actionable metrics that demonstrate clear cause and effect</li>\\n        <li>Practice continuous deployment and small batch sizes</li>\\n        <li>Be willing to pivot when necessary based on validated learning</li>\\n      </ul>\\n\\n      <p><strong>For Established Companies:</strong></p>\\n      <ul>\\n        <li>Create innovation teams with appropriate structures and metrics</li>\\n        <li>Allocate resources using innovation accounting</li>\\n        <li>Develop internal entrepreneurship through dedicated teams</li>\\n        <li>Apply lean principles to accelerate product development cycles</li>\\n      </ul>\\n    \",\n        isPremium: true\n    }\n];\n// Convert to BookPreview format for listing pages\nconst mockBookPreviews = mockBooks.map((book)=>({\n        id: book.id,\n        title: book.title,\n        author: book.author,\n        coverUrl: book.coverUrl,\n        category: book.category,\n        rating: book.rating\n    }));\nconst mockBusinessPlans = [\n    {\n        id: \"small-1\",\n        title: \"Local Coffee Shop\",\n        category: \"Food & Beverage\",\n        size: \"small\",\n        description: \"A comprehensive business plan for starting and operating a successful local coffee shop.\",\n        overview: \"\\n      <h3>Executive Summary</h3>\\n      <p>This business plan outlines the establishment of a cozy, community-focused coffee shop that serves premium coffee and light food items. The shop will be located in a high-traffic area with significant foot traffic and limited direct competition.</p>\\n\\n      <h3>Business Concept</h3>\\n      <ul>\\n        <li>Premium coffee and espresso drinks</li>\\n        <li>Fresh pastries and light meals</li>\\n        <li>Comfortable seating and free Wi-Fi</li>\\n        <li>Focus on sustainable practices</li>\\n      </ul>\\n\\n      <h3>Target Market</h3>\\n      <ul>\\n        <li>Young professionals (25-40)</li>\\n        <li>College students</li>\\n        <li>Remote workers</li>\\n        <li>Local residents</li>\\n      </ul>\\n    \",\n        marketAnalysis: \"\\n      <h3>Market Overview</h3>\\n      <p>The coffee shop industry continues to grow, with increasing demand for premium coffee experiences. Key market trends include:</p>\\n      <ul>\\n        <li>Growing preference for specialty coffee</li>\\n        <li>Increased focus on sustainability</li>\\n        <li>Rising demand for plant-based options</li>\\n        <li>Need for comfortable workspaces</li>\\n      </ul>\\n\\n      <h3>Competitive Analysis</h3>\\n      <p>Local competition includes:</p>\\n      <ul>\\n        <li>Chain coffee shops (2 within 1km)</li>\\n        <li>Independent cafes (1 within 1km)</li>\\n        <li>Restaurants serving coffee</li>\\n      </ul>\\n\\n      <h3>Competitive Advantage</h3>\\n      <ul>\\n        <li>Premium quality coffee</li>\\n        <li>Comfortable atmosphere</li>\\n        <li>Excellent customer service</li>\\n        <li>Strategic location</li>\\n      </ul>\\n    \",\n        financials: \"\\n      <h3>Startup Costs</h3>\\n      <ul>\\n        <li>Lease deposit and improvements: $25,000</li>\\n        <li>Equipment: $35,000</li>\\n        <li>Initial inventory: $5,000</li>\\n        <li>Licenses and permits: $2,000</li>\\n        <li>Working capital: $20,000</li>\\n      </ul>\\n\\n      <h3>Financial Projections</h3>\\n      <p>Year 1:</p>\\n      <ul>\\n        <li>Revenue: $300,000</li>\\n        <li>Expenses: $270,000</li>\\n        <li>Net profit: $30,000</li>\\n      </ul>\\n\\n      <p>Year 2:</p>\\n      <ul>\\n        <li>Revenue: $400,000</li>\\n        <li>Expenses: $340,000</li>\\n        <li>Net profit: $60,000</li>\\n      </ul>\\n    \",\n        implementation: \"\\n      <h3>Timeline</h3>\\n      <ul>\\n        <li>Month 1-2: Location selection and lease signing</li>\\n        <li>Month 2-3: Design and permits</li>\\n        <li>Month 3-4: Construction and equipment installation</li>\\n        <li>Month 4: Staff hiring and training</li>\\n        <li>Month 5: Soft opening and marketing</li>\\n        <li>Month 6: Grand opening</li>\\n      </ul>\\n\\n      <h3>Marketing Strategy</h3>\\n      <ul>\\n        <li>Social media presence</li>\\n        <li>Local partnerships</li>\\n        <li>Loyalty program</li>\\n        <li>Community events</li>\\n      </ul>\\n\\n      <h3>Risk Mitigation</h3>\\n      <ul>\\n        <li>Comprehensive insurance coverage</li>\\n        <li>Diverse supplier relationships</li>\\n        <li>Staff training programs</li>\\n        <li>Cash flow management</li>\\n      </ul>\\n    \",\n        isPremium: false\n    },\n    {\n        id: \"small-2\",\n        title: \"Freelance Web Development\",\n        category: \"Technology\",\n        size: \"small\",\n        description: \"A detailed business plan for starting and growing a freelance web development business.\",\n        overview: \"\\n      <h3>Executive Summary</h3>\\n      <p>This business plan outlines the establishment of a freelance web development business focused on creating custom websites and web applications for small to medium-sized businesses. The business will operate remotely with minimal overhead costs.</p>\\n\\n      <h3>Business Concept</h3>\\n      <ul>\\n        <li>Custom website development</li>\\n        <li>Web application development</li>\\n        <li>Website maintenance and support</li>\\n        <li>SEO and digital marketing services</li>\\n      </ul>\\n\\n      <h3>Target Market</h3>\\n      <ul>\\n        <li>Small businesses needing online presence</li>\\n        <li>Medium-sized companies requiring web applications</li>\\n        <li>Startups with limited budgets</li>\\n        <li>Non-profit organizations</li>\\n      </ul>\\n    \",\n        marketAnalysis: \"\\n      <h3>Market Overview</h3>\\n      <p>The web development industry continues to grow as businesses increasingly recognize the importance of online presence. Key market trends include:</p>\\n      <ul>\\n        <li>Increasing demand for mobile-responsive websites</li>\\n        <li>Growing need for e-commerce functionality</li>\\n        <li>Rising importance of user experience (UX) design</li>\\n        <li>Shift toward progressive web applications</li>\\n      </ul>\\n\\n      <h3>Competitive Analysis</h3>\\n      <p>Competition includes:</p>\\n      <ul>\\n        <li>Other freelance developers</li>\\n        <li>Web development agencies</li>\\n        <li>DIY website builders (Wix, Squarespace)</li>\\n      </ul>\\n\\n      <h3>Competitive Advantage</h3>\\n      <ul>\\n        <li>Personalized service and direct client communication</li>\\n        <li>Lower overhead costs than agencies</li>\\n        <li>Specialized expertise in modern frameworks</li>\\n        <li>Flexible pricing models</li>\\n      </ul>\\n    \",\n        financials: \"\\n      <h3>Startup Costs</h3>\\n      <ul>\\n        <li>Computer equipment: $3,000</li>\\n        <li>Software subscriptions: $1,200/year</li>\\n        <li>Website and hosting: $500</li>\\n        <li>Business registration: $300</li>\\n        <li>Initial marketing: $1,000</li>\\n      </ul>\\n\\n      <h3>Financial Projections</h3>\\n      <p>Year 1:</p>\\n      <ul>\\n        <li>Revenue: $60,000</li>\\n        <li>Expenses: $15,000</li>\\n        <li>Net profit: $45,000</li>\\n      </ul>\\n\\n      <p>Year 2:</p>\\n      <ul>\\n        <li>Revenue: $90,000</li>\\n        <li>Expenses: $20,000</li>\\n        <li>Net profit: $70,000</li>\\n      </ul>\\n    \",\n        implementation: \"\\n      <h3>Timeline</h3>\\n      <ul>\\n        <li>Month 1: Business registration and website setup</li>\\n        <li>Month 2: Portfolio development</li>\\n        <li>Month 3: Initial marketing and networking</li>\\n        <li>Month 4-6: Secure first clients and build reputation</li>\\n      </ul>\\n\\n      <h3>Marketing Strategy</h3>\\n      <ul>\\n        <li>Portfolio website showcasing work</li>\\n        <li>Social media presence on LinkedIn and Twitter</li>\\n        <li>Content marketing through blog posts</li>\\n        <li>Networking at local business events</li>\\n        <li>Referral program for existing clients</li>\\n      </ul>\\n\\n      <h3>Risk Mitigation</h3>\\n      <ul>\\n        <li>Diversify client base to avoid dependency</li>\\n        <li>Maintain emergency fund for slow periods</li>\\n        <li>Continuous skill development</li>\\n        <li>Clear contracts and scope definitions</li>\\n      </ul>\\n    \",\n        isPremium: false\n    }\n];\nconst mockBusinessPlanPreviews = mockBusinessPlans.map((plan)=>({\n        id: plan.id,\n        title: plan.title,\n        category: plan.category,\n        size: plan.size,\n        description: plan.description,\n        isPremium: plan.isPremium\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/mock-data.ts\n"));

/***/ })

});