"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/books/[slug]/page",{

/***/ "(app-pages-browser)/./app/books/[slug]/page.tsx":
/*!***********************************!*\
  !*** ./app/books/[slug]/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BookPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronLeft,Lightbulb,Loader2,MessageSquare,Sparkles,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _components_book_bookmark__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/book-bookmark */ \"(app-pages-browser)/./components/book-bookmark.tsx\");\n/* harmony import */ var _components_offline_book_toggle__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/offline-book-toggle */ \"(app-pages-browser)/./components/offline-book-toggle.tsx\");\n/* harmony import */ var _lib_offline_storage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/offline-storage */ \"(app-pages-browser)/./lib/offline-storage.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _components_reading_progress_bar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/reading-progress-bar */ \"(app-pages-browser)/./components/reading-progress-bar.tsx\");\n/* harmony import */ var _components_highlight_controls__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/highlight-controls */ \"(app-pages-browser)/./components/highlight-controls.tsx\");\n/* harmony import */ var _components_notes_panel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/notes-panel */ \"(app-pages-browser)/./components/notes-panel.tsx\");\n/* harmony import */ var _components_text_to_speech_player__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/text-to-speech-player */ \"(app-pages-browser)/./components/text-to-speech-player.tsx\");\n/* harmony import */ var _components_flashcard_creator__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/flashcard-creator */ \"(app-pages-browser)/./components/flashcard-creator.tsx\");\n/* harmony import */ var _components_reading_speed_tracker__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/reading-speed-tracker */ \"(app-pages-browser)/./components/reading-speed-tracker.tsx\");\n/* harmony import */ var _components_social_sharing__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/social-sharing */ \"(app-pages-browser)/./components/social-sharing.tsx\");\n/* harmony import */ var _components_comprehension_quiz__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/comprehension-quiz */ \"(app-pages-browser)/./components/comprehension-quiz.tsx\");\n/* harmony import */ var _components_recommendations_similar_content__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/recommendations/similar-content */ \"(app-pages-browser)/./components/recommendations/similar-content.tsx\");\n/* harmony import */ var _lib_analytics__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/lib/analytics */ \"(app-pages-browser)/./lib/analytics.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction BookPage(param) {\n    let { params } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [book, setBook] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [fontSize, setFontSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"medium\");\n    const [isOffline, setIsOffline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOfflineAvailable, setIsOfflineAvailable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [readingProgress, setReadingProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showNotes, setShowNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isHighlighting, setIsHighlighting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"reading\");\n    const [selectedTextForSharing, setSelectedTextForSharing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isTtsPlaying, setIsTtsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [readingStartTime, setReadingStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [readingSessionId, setReadingSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Check online status\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BookPage.useEffect\": ()=>{\n            setIsOffline(!navigator.onLine);\n            const handleOnline = {\n                \"BookPage.useEffect.handleOnline\": ()=>setIsOffline(false)\n            }[\"BookPage.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"BookPage.useEffect.handleOffline\": ()=>setIsOffline(true)\n            }[\"BookPage.useEffect.handleOffline\"];\n            window.addEventListener(\"online\", handleOnline);\n            window.addEventListener(\"offline\", handleOffline);\n            return ({\n                \"BookPage.useEffect\": ()=>{\n                    window.removeEventListener(\"online\", handleOnline);\n                    window.removeEventListener(\"offline\", handleOffline);\n                }\n            })[\"BookPage.useEffect\"];\n        }\n    }[\"BookPage.useEffect\"], []);\n    // Fetch book data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BookPage.useEffect\": ()=>{\n            const fetchBook = {\n                \"BookPage.useEffect.fetchBook\": async ()=>{\n                    try {\n                        var _bookData_categories;\n                        setIsLoading(true);\n                        // First check if book is available offline\n                        const offlineAvailable = await (0,_lib_offline_storage__WEBPACK_IMPORTED_MODULE_9__.isBookAvailableOffline)(params.slug);\n                        setIsOfflineAvailable(offlineAvailable);\n                        if (isOffline && !offlineAvailable) {\n                            // If offline and book not available offline\n                            setError(\"You're offline and this book isn't available offline. Please connect to the internet or choose a book you've saved for offline reading.\");\n                            setIsLoading(false);\n                            return;\n                        }\n                        let bookData = null;\n                        if (offlineAvailable) {\n                            // Get from offline storage\n                            bookData = await (0,_lib_offline_storage__WEBPACK_IMPORTED_MODULE_9__.getOfflineBook)(params.slug);\n                        } else {\n                            // Fetch from API\n                            const response = await fetch(\"/api/books/\".concat(params.slug));\n                            if (!response.ok) {\n                                if (response.status === 404) {\n                                    throw new Error(\"Book not found\");\n                                }\n                                throw new Error(\"Failed to fetch book details\");\n                            }\n                            bookData = await response.json();\n                        }\n                        setBook(bookData);\n                        // Load reading progress\n                        const progress = await (0,_lib_offline_storage__WEBPACK_IMPORTED_MODULE_9__.getReadingProgress)(params.slug);\n                        if (progress) {\n                            setReadingProgress(progress);\n                        }\n                        // Track view activity\n                        // In a real app, we would get the user ID from authentication\n                        const mockUserId = \"user-123\";\n                        (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"view\", bookData.id, \"book\", {\n                            title: bookData.title,\n                            category: ((_bookData_categories = bookData.categories) === null || _bookData_categories === void 0 ? void 0 : _bookData_categories[0]) || \"Uncategorized\"\n                        });\n                        // Start reading session\n                        setReadingStartTime(new Date());\n                        setReadingSessionId(\"reading-session-\".concat(Date.now()));\n                    } catch (err) {\n                        console.error(\"Error fetching book:\", err);\n                        setError(\"Error loading book details. Please try again later.\");\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"BookPage.useEffect.fetchBook\"];\n            fetchBook();\n            // Cleanup function to track reading time when component unmounts\n            return ({\n                \"BookPage.useEffect\": ()=>{\n                    if (book && readingStartTime) {\n                        const endTime = new Date();\n                        const timeSpentMinutes = Math.round((endTime.getTime() - readingStartTime.getTime()) / 60000);\n                        if (timeSpentMinutes > 0) {\n                            var _book_categories;\n                            // Track read activity with time spent\n                            const mockUserId = \"user-123\";\n                            (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"read\", book.id, \"book\", {\n                                title: book.title,\n                                category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\",\n                                timeSpent: timeSpentMinutes,\n                                progress: (readingProgress === null || readingProgress === void 0 ? void 0 : readingProgress.completionPercentage) || 0,\n                                sessionId: readingSessionId\n                            });\n                        }\n                    }\n                }\n            })[\"BookPage.useEffect\"];\n        }\n    }[\"BookPage.useEffect\"], [\n        params.slug,\n        isOffline\n    ]);\n    // Save reading progress periodically\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BookPage.useEffect\": ()=>{\n            if (!book || !contentRef.current) return;\n            const content = contentRef.current;\n            const totalLength = content.scrollHeight;\n            const saveProgress = {\n                \"BookPage.useEffect.saveProgress\": async ()=>{\n                    const position = content.scrollTop;\n                    const completionPercentage = Math.min(100, Math.round(position / (totalLength - content.clientHeight) * 100));\n                    const progress = {\n                        position,\n                        totalLength,\n                        lastReadAt: new Date().toISOString(),\n                        completionPercentage: isNaN(completionPercentage) ? 0 : completionPercentage\n                    };\n                    setReadingProgress(progress);\n                    try {\n                        await (0,_lib_offline_storage__WEBPACK_IMPORTED_MODULE_9__.saveReadingProgress)(book.id, progress);\n                        // Track progress update if significant change (every 10%)\n                        if (progress.completionPercentage % 10 === 0) {\n                            var _book_categories;\n                            const mockUserId = \"user-123\";\n                            (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"read\", book.id, \"book\", {\n                                title: book.title,\n                                category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\",\n                                progress: progress.completionPercentage,\n                                sessionId: readingSessionId\n                            });\n                            // If completed, track completion\n                            if (progress.completionPercentage >= 90) {\n                                var _book_categories1;\n                                (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"complete\", book.id, \"book\", {\n                                    title: book.title,\n                                    category: ((_book_categories1 = book.categories) === null || _book_categories1 === void 0 ? void 0 : _book_categories1[0]) || \"Uncategorized\"\n                                });\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Error saving reading progress:\", error);\n                    }\n                }\n            }[\"BookPage.useEffect.saveProgress\"];\n            // Save progress on scroll (debounced)\n            let timeout;\n            const handleScroll = {\n                \"BookPage.useEffect.handleScroll\": ()=>{\n                    clearTimeout(timeout);\n                    timeout = setTimeout(saveProgress, 500);\n                }\n            }[\"BookPage.useEffect.handleScroll\"];\n            content.addEventListener(\"scroll\", handleScroll);\n            // Restore scroll position from saved progress\n            if (readingProgress === null || readingProgress === void 0 ? void 0 : readingProgress.position) {\n                content.scrollTop = readingProgress.position;\n            }\n            // Save progress when leaving the page\n            const handleBeforeUnload = {\n                \"BookPage.useEffect.handleBeforeUnload\": ()=>{\n                    saveProgress();\n                }\n            }[\"BookPage.useEffect.handleBeforeUnload\"];\n            window.addEventListener(\"beforeunload\", handleBeforeUnload);\n            return ({\n                \"BookPage.useEffect\": ()=>{\n                    content.removeEventListener(\"scroll\", handleScroll);\n                    window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n                    clearTimeout(timeout);\n                    saveProgress();\n                }\n            })[\"BookPage.useEffect\"];\n        }\n    }[\"BookPage.useEffect\"], [\n        book,\n        readingProgress === null || readingProgress === void 0 ? void 0 : readingProgress.position,\n        readingSessionId\n    ]);\n    // Handle text selection for sharing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BookPage.useEffect\": ()=>{\n            const handleSelection = {\n                \"BookPage.useEffect.handleSelection\": ()=>{\n                    const selection = window.getSelection();\n                    if (selection && !selection.isCollapsed) {\n                        setSelectedTextForSharing(selection.toString());\n                    }\n                }\n            }[\"BookPage.useEffect.handleSelection\"];\n            document.addEventListener(\"mouseup\", handleSelection);\n            return ({\n                \"BookPage.useEffect\": ()=>{\n                    document.removeEventListener(\"mouseup\", handleSelection);\n                }\n            })[\"BookPage.useEffect\"];\n        }\n    }[\"BookPage.useEffect\"], []);\n    const fontSizeClasses = {\n        small: \"text-sm leading-6\",\n        medium: \"text-base leading-7\",\n        large: \"text-lg leading-8\"\n    };\n    const handleOfflineStatusChange = (available)=>{\n        setIsOfflineAvailable(available);\n        // Track download activity if made available offline\n        if (available && book) {\n            var _book_categories;\n            const mockUserId = \"user-123\";\n            (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"download\", book.id, \"book\", {\n                title: book.title,\n                category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\"\n            });\n        }\n    };\n    const handleTtsPlayingChange = (playing)=>{\n        setIsTtsPlaying(playing);\n    };\n    // Estimate word count for reading speed tracking\n    const getWordCount = ()=>{\n        if (!book) return 0;\n        // Strip HTML tags and count words\n        const text = book.summary.replace(/<[^>]*>/g, \"\");\n        return text.split(/\\s+/).length;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-8 md:py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/books\",\n                    className: \"inline-flex items-center gap-1 text-muted-foreground hover:text-foreground mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Back to Books\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center items-center h-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            className: \"h-8 w-8 animate-spin text-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"ml-2\",\n                            children: \"Loading book details...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n            lineNumber: 280,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !book) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-8 md:py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/books\",\n                    className: \"inline-flex items-center gap-1 text-muted-foreground hover:text-foreground mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Back to Books\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-destructive mb-4\",\n                            children: error || \"Book not found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>router.push(\"/books\"),\n                            children: \"Browse Books\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n            lineNumber: 296,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-8 md:py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/books\",\n                            className: \"inline-flex items-center gap-1 text-muted-foreground hover:text-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Back to Books\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: isOffline ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 px-2 py-1 rounded-full text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Offline Mode\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 px-2 py-1 rounded-full text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Online\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, this),\n                readingProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_reading_progress_bar__WEBPACK_IMPORTED_MODULE_11__.ReadingProgressBar, {\n                    progress: readingProgress.completionPercentage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 29\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sticky top-24 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: book.coverUrl || \"/placeholder.svg\",\n                                                alt: \"\".concat(book.title, \" book cover\"),\n                                                className: \"w-full max-w-[240px] mx-auto rounded-xl shadow-md card-themed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 space-y-4 gradient-text-overlay p-4 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: book.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: book.author\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-4 text-sm text-muted-foreground\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-primary font-semibold\",\n                                                                        children: book.rating\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        width: \"16\",\n                                                                        height: \"16\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"currentColor\",\n                                                                        stroke: \"none\",\n                                                                        className: \"text-secondary\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                            points: \"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 364,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 355,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        book.pages,\n                                                                        \" pages\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: book.language\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-4 flex flex-wrap gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_book_bookmark__WEBPACK_IMPORTED_MODULE_7__.BookBookmark, {\n                                                                bookId: book.id,\n                                                                bookTitle: book.title,\n                                                                onBookmark: ()=>{\n                                                                    var _book_categories;\n                                                                    // Track bookmark activity\n                                                                    const mockUserId = \"user-123\";\n                                                                    (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"bookmark\", book.id, \"book\", {\n                                                                        title: book.title,\n                                                                        category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\"\n                                                                    });\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_offline_book_toggle__WEBPACK_IMPORTED_MODULE_8__.OfflineBookToggle, {\n                                                                book: book,\n                                                                onStatusChange: handleOfflineStatusChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_social_sharing__WEBPACK_IMPORTED_MODULE_17__.SocialSharing, {\n                                                                contentId: book.id,\n                                                                contentTitle: book.title,\n                                                                contentType: \"book\",\n                                                                quote: selectedTextForSharing || undefined\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === \"reading\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_reading_speed_tracker__WEBPACK_IMPORTED_MODULE_16__.ReadingSpeedTracker, {\n                                        contentId: book.id,\n                                        contentLength: getWordCount()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 43\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_recommendations_similar_content__WEBPACK_IMPORTED_MODULE_19__.SimilarContent, {\n                                        contentId: book.id,\n                                        contentType: \"book\",\n                                        contentTitle: book.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                                defaultValue: \"reading\",\n                                value: activeTab,\n                                onValueChange: setActiveTab,\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"reading\",\n                                                className: \"gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Reading\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"tools\",\n                                                className: \"gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Learning Tools\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"quiz\",\n                                                className: \"gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Quiz\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"reading\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                            className: \"p-6 rounded-xl border-none shadow-sm card-themed\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: fontSize === \"small\" ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"rounded-full h-8 w-8 p-0\",\n                                                                    onClick: ()=>setFontSize(\"small\"),\n                                                                    children: \"S\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 426,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: fontSize === \"medium\" ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"rounded-full h-8 w-8 p-0\",\n                                                                    onClick: ()=>setFontSize(\"medium\"),\n                                                                    children: \"M\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: fontSize === \"large\" ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"rounded-full h-8 w-8 p-0\",\n                                                                    onClick: ()=>setFontSize(\"large\"),\n                                                                    children: \"L\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: isHighlighting ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"gap-1\",\n                                                                    onClick: ()=>setIsHighlighting(!isHighlighting),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"2\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"m9 11-6 6v3h9l3-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 470,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"m22 12-4.6 4.6a2 2 0 0 1-2.8 0l-5.2-5.2a2 2 0 0 1 0-2.8L14 4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 471,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 459,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Highlight\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 473,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: showNotes ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"gap-1\",\n                                                                    onClick: ()=>{\n                                                                        setShowNotes(!showNotes);\n                                                                        // Track note creation activity\n                                                                        if (!showNotes && book) {\n                                                                            var _book_categories;\n                                                                            const mockUserId = \"user-123\";\n                                                                            (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"note_create\", book.id, \"book\", {\n                                                                                title: book.title,\n                                                                                category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\"\n                                                                            });\n                                                                        }\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronLeft_Lightbulb_Loader2_MessageSquare_Sparkles_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 493,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Notes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 494,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 \".concat(showNotes ? \"pr-4\" : \"\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                                                                defaultValue: \"summary\",\n                                                                className: \"w-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                                                        className: \"mb-4 bg-muted/50 p-1 rounded-full\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                                                value: \"summary\",\n                                                                                className: \"rounded-full\",\n                                                                                children: \"Summary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 503,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                                                value: \"key-insights\",\n                                                                                className: \"rounded-full\",\n                                                                                children: \"Key Insights\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 506,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                                                value: \"applications\",\n                                                                                className: \"rounded-full\",\n                                                                                children: \"Applications\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 509,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 502,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        ref: contentRef,\n                                                                        className: \"max-h-[70vh] overflow-y-auto pr-4 custom-scrollbar\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                                                                value: \"summary\",\n                                                                                className: \"\".concat(fontSizeClasses[fontSize], \" space-y-4\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    dangerouslySetInnerHTML: {\n                                                                                        __html: book.summary\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 516,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 515,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                                                                value: \"key-insights\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"\".concat(fontSizeClasses[fontSize], \" space-y-4\"),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        dangerouslySetInnerHTML: {\n                                                                                            __html: book.keyInsights || book.summary\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                        lineNumber: 521,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 520,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 519,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                                                                value: \"applications\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"\".concat(fontSizeClasses[fontSize], \" space-y-4\"),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        dangerouslySetInnerHTML: {\n                                                                                            __html: book.applications || \"\"\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                        lineNumber: 527,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 526,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 525,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 514,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showNotes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-64 border-l pl-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notes_panel__WEBPACK_IMPORTED_MODULE_13__.NotesPanel, {\n                                                                contentId: book.id\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isHighlighting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 border-t pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_highlight_controls__WEBPACK_IMPORTED_MODULE_12__.HighlightControls, {\n                                                        contentId: book.id\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 21\n                                                }, this),\n                                                !isTtsPlaying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 border-t pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_text_to_speech_player__WEBPACK_IMPORTED_MODULE_14__.TextToSpeechPlayer, {\n                                                        text: book.summary.replace(/<[^>]*>/g, \"\"),\n                                                        onPlayingChange: handleTtsPlayingChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"tools\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                    className: \"p-6 rounded-xl border-none shadow-sm card-themed\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-bold mb-4\",\n                                                            children: \"Flashcards\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flashcard_creator__WEBPACK_IMPORTED_MODULE_15__.FlashcardCreator, {\n                                                            contentId: book.id,\n                                                            contentTitle: book.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                    className: \"p-6 rounded-xl border-none shadow-sm card-themed\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-bold mb-4\",\n                                                            children: \"Text-to-Speech\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_text_to_speech_player__WEBPACK_IMPORTED_MODULE_14__.TextToSpeechPlayer, {\n                                                            text: book.summary.replace(/<[^>]*>/g, \"\"),\n                                                            onPlayingChange: handleTtsPlayingChange\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"quiz\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                            className: \"p-6 rounded-xl border-none shadow-sm card-themed\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold mb-4\",\n                                                    children: \"Test Your Understanding\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_comprehension_quiz__WEBPACK_IMPORTED_MODULE_18__.ComprehensionQuiz, {\n                                                    contentId: book.id,\n                                                    contentTitle: book.title,\n                                                    contentType: \"book\",\n                                                    onQuizAttempt: ()=>{\n                                                        var _book_categories;\n                                                        // Track quiz attempt activity\n                                                        const mockUserId = \"user-123\";\n                                                        (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_20__.trackUserActivity)(mockUserId, \"quiz_attempt\", book.id, \"book\", {\n                                                            title: book.title,\n                                                            category: ((_book_categories = book.categories) === null || _book_categories === void 0 ? void 0 : _book_categories[0]) || \"Uncategorized\"\n                                                        });\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n            lineNumber: 314,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\",\n        lineNumber: 313,\n        columnNumber: 5\n    }, this);\n}\n_s(BookPage, \"yR5tjC9fYAccchtyl4fak/7BNFU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = BookPage;\nvar _c;\n$RefreshReg$(_c, \"BookPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/books/[slug]/page.tsx\n"));

/***/ })

});