{"version": 3, "sources": ["../../../src/server/use-cache/use-cache-wrapper.ts"], "sourcesContent": ["import type { DeepReadonly } from '../../shared/lib/deep-readonly'\n/* eslint-disable import/no-extraneous-dependencies */\nimport {\n  renderToReadableStream,\n  decodeReply,\n  createTemporaryReferenceSet as createServerTemporaryReferenceSet,\n} from 'react-server-dom-webpack/server.edge'\n/* eslint-disable import/no-extraneous-dependencies */\nimport {\n  createFromReadableStream,\n  encodeReply,\n  createTemporaryReferenceSet as createClientTemporaryReferenceSet,\n} from 'react-server-dom-webpack/client.edge'\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport type {\n  UseCacheStore,\n  WorkUnitStore,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  getRenderResumeDataCache,\n  getPrerenderResumeDataCache,\n  workUnitAsyncStorage,\n} from '../app-render/work-unit-async-storage.external'\nimport { runInCleanSnapshot } from '../app-render/clean-async-snapshot.external'\n\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\n\nimport type { ClientReferenceManifestForRsc } from '../../build/webpack/plugins/flight-manifest-plugin'\n\nimport {\n  getClientReferenceManifestForRsc,\n  getServerModuleMap,\n} from '../app-render/encryption-utils'\nimport DefaultCacheHandler from '../lib/cache-handlers/default'\nimport type { CacheHandler, CacheEntry } from '../lib/cache-handlers/types'\nimport type { CacheSignal } from '../app-render/cache-signal'\nimport { decryptActionBoundArgs } from '../app-render/encryption'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { getDigestForWellKnownError } from '../app-render/create-error-handler'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\n// If the expire time is less than .\nconst DYNAMIC_EXPIRE = 300\n\nconst cacheHandlersSymbol = Symbol.for('@next/cache-handlers')\nconst _globalThis: typeof globalThis & {\n  [cacheHandlersSymbol]?: {\n    RemoteCache?: CacheHandler\n    DefaultCache?: CacheHandler\n  }\n  __nextCacheHandlers?: Record<string, CacheHandler>\n} = globalThis\n\nconst cacheHandlerMap: Map<string, CacheHandler> = new Map([\n  [\n    'default',\n    _globalThis[cacheHandlersSymbol]?.DefaultCache || DefaultCacheHandler,\n  ],\n  [\n    'remote',\n    // in dev remote maps to default handler\n    // and is meant to be overridden in prod\n    _globalThis[cacheHandlersSymbol]?.RemoteCache || DefaultCacheHandler,\n  ],\n])\n\nfunction generateCacheEntry(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: any\n): Promise<[ReadableStream, Promise<CacheEntry>]> {\n  // We need to run this inside a clean AsyncLocalStorage snapshot so that the cache\n  // generation cannot read anything from the context we're currently executing which\n  // might include request specific things like cookies() inside a React.cache().\n  // Note: It is important that we await at least once before this because it lets us\n  // pop out of any stack specific contexts as well - aka \"Sync\" Local Storage.\n  return runInCleanSnapshot(\n    generateCacheEntryWithRestoredWorkStore,\n    workStore,\n    outerWorkUnitStore,\n    clientReferenceManifest,\n    encodedArguments,\n    fn\n  )\n}\n\nfunction generateCacheEntryWithRestoredWorkStore(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: any\n) {\n  // Since we cleared the AsyncLocalStorage we need to restore the workStore.\n  // Note: We explicitly don't restore the RequestStore nor the PrerenderStore.\n  // We don't want any request specific information leaking an we don't want to create a\n  // bloated fake request mock for every cache call. So any feature that currently lives\n  // in RequestStore but should be available to Caches need to move to WorkStore.\n  // PrerenderStore is not needed inside the cache scope because the outer most one will\n  // be the one to report its result to the outer Prerender.\n  return workAsyncStorage.run(\n    workStore,\n    generateCacheEntryWithCacheContext,\n    workStore,\n    outerWorkUnitStore,\n    clientReferenceManifest,\n    encodedArguments,\n    fn\n  )\n}\n\nfunction generateCacheEntryWithCacheContext(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: any\n) {\n  if (!workStore.cacheLifeProfiles) {\n    throw new Error(\n      'cacheLifeProfiles should always be provided. This is a bug in Next.js.'\n    )\n  }\n  const defaultCacheLife = workStore.cacheLifeProfiles['default']\n  if (\n    !defaultCacheLife ||\n    defaultCacheLife.revalidate == null ||\n    defaultCacheLife.expire == null ||\n    defaultCacheLife.stale == null\n  ) {\n    throw new Error(\n      'A default cacheLife profile must always be provided. This is a bug in Next.js.'\n    )\n  }\n\n  // Initialize the Store for this Cache entry.\n  const cacheStore: UseCacheStore = {\n    type: 'cache',\n    phase: 'render',\n    implicitTags:\n      outerWorkUnitStore === undefined ||\n      outerWorkUnitStore.type === 'unstable-cache'\n        ? []\n        : outerWorkUnitStore.implicitTags,\n    revalidate: defaultCacheLife.revalidate,\n    expire: defaultCacheLife.expire,\n    stale: defaultCacheLife.stale,\n    explicitRevalidate: undefined,\n    explicitExpire: undefined,\n    explicitStale: undefined,\n    tags: null,\n  }\n  return workUnitAsyncStorage.run(\n    cacheStore,\n    generateCacheEntryImpl,\n    workStore,\n    outerWorkUnitStore,\n    cacheStore,\n    clientReferenceManifest,\n    encodedArguments,\n    fn\n  )\n}\n\nfunction propagateCacheLifeAndTags(\n  workUnitStore: WorkUnitStore | undefined,\n  entry: CacheEntry\n): void {\n  if (\n    workUnitStore &&\n    (workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-ppr' ||\n      workUnitStore.type === 'prerender-legacy')\n  ) {\n    // Propagate tags and revalidate upwards\n    const outerTags = workUnitStore.tags ?? (workUnitStore.tags = [])\n    const entryTags = entry.tags\n    for (let i = 0; i < entryTags.length; i++) {\n      const tag = entryTags[i]\n      if (!outerTags.includes(tag)) {\n        outerTags.push(tag)\n      }\n    }\n    if (workUnitStore.stale > entry.stale) {\n      workUnitStore.stale = entry.stale\n    }\n    if (workUnitStore.revalidate > entry.revalidate) {\n      workUnitStore.revalidate = entry.revalidate\n    }\n    if (workUnitStore.expire > entry.expire) {\n      workUnitStore.expire = entry.expire\n    }\n  }\n}\n\nasync function collectResult(\n  savedStream: ReadableStream,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  innerCacheStore: UseCacheStore,\n  startTime: number,\n  errors: Array<unknown>, // This is a live array that gets pushed into.,\n  timer: any\n): Promise<CacheEntry> {\n  // We create a buffered stream that collects all chunks until the end to\n  // ensure that RSC has finished rendering and therefore we have collected\n  // all tags. In the future the RSC API might allow for the equivalent of\n  // the allReady Promise that exists on SSR streams.\n  //\n  // If something errored or rejected anywhere in the render, we close\n  // the stream as errored. This lets a CacheHandler choose to save the\n  // partial result up until that point for future hits for a while to avoid\n  // unnecessary retries or not to retry. We use the end of the stream for\n  // this to avoid another complicated side-channel. A receiver has to consider\n  // that the stream might also error for other reasons anyway such as losing\n  // connection.\n\n  const buffer: any[] = []\n  const reader = savedStream.getReader()\n  for (let entry; !(entry = await reader.read()).done; ) {\n    buffer.push(entry.value)\n  }\n\n  let idx = 0\n  const bufferStream = new ReadableStream({\n    pull(controller) {\n      if (idx < buffer.length) {\n        controller.enqueue(buffer[idx++])\n      } else if (errors.length > 0) {\n        // TODO: Should we use AggregateError here?\n        controller.error(errors[0])\n      } else {\n        controller.close()\n      }\n    },\n  })\n\n  const collectedTags = innerCacheStore.tags\n  // If cacheLife() was used to set an explicit revalidate time we use that.\n  // Otherwise, we use the lowest of all inner fetch()/unstable_cache() or nested \"use cache\".\n  // If they're lower than our default.\n  const collectedRevalidate =\n    innerCacheStore.explicitRevalidate !== undefined\n      ? innerCacheStore.explicitRevalidate\n      : innerCacheStore.revalidate\n  const collectedExpire =\n    innerCacheStore.explicitExpire !== undefined\n      ? innerCacheStore.explicitExpire\n      : innerCacheStore.expire\n  const collectedStale =\n    innerCacheStore.explicitStale !== undefined\n      ? innerCacheStore.explicitStale\n      : innerCacheStore.stale\n\n  const entry = {\n    value: bufferStream,\n    timestamp: startTime,\n    revalidate: collectedRevalidate,\n    expire: collectedExpire,\n    stale: collectedStale,\n    tags: collectedTags === null ? [] : collectedTags,\n  }\n  // Propagate tags/revalidate to the parent context.\n  propagateCacheLifeAndTags(outerWorkUnitStore, entry)\n\n  const cacheSignal =\n    outerWorkUnitStore && outerWorkUnitStore.type === 'prerender'\n      ? outerWorkUnitStore.cacheSignal\n      : null\n  if (cacheSignal) {\n    cacheSignal.endRead()\n  }\n\n  if (timer !== undefined) {\n    clearTimeout(timer)\n  }\n\n  return entry\n}\n\nasync function generateCacheEntryImpl(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  innerCacheStore: UseCacheStore,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: any\n): Promise<[ReadableStream, Promise<CacheEntry>]> {\n  const temporaryReferences = createServerTemporaryReferenceSet()\n\n  const [, , args] = await decodeReply<any[]>(\n    encodedArguments,\n    getServerModuleMap(),\n    {\n      temporaryReferences,\n    }\n  )\n\n  // Track the timestamp when we started copmuting the result.\n  const startTime = performance.timeOrigin + performance.now()\n  // Invoke the inner function to load a new result.\n  const result = fn.apply(null, args)\n\n  let errors: Array<unknown> = []\n\n  let timer = undefined\n  const controller = new AbortController()\n  if (workStore.isStaticGeneration) {\n    // If we're prerendering, we give you 50 seconds to fill a cache entry. Otherwise\n    // we assume you stalled on hanging input and deopt. This needs to be lower than\n    // just the general timeout of 60 seconds.\n    timer = setTimeout(() => {\n      controller.abort(\n        new Error(\n          'Filling a cache during prerender timed out, likely because request-specific arguments such as ' +\n            'params, searchParams, cookies() or dynamic data were used inside \"use cache\".'\n        )\n      )\n    }, 50000)\n  }\n\n  const stream = renderToReadableStream(\n    result,\n    clientReferenceManifest.clientModules,\n    {\n      environmentName: 'Cache',\n      signal: controller.signal,\n      temporaryReferences,\n      // In the \"Cache\" environment, we only need to make sure that the error\n      // digests are handled correctly. Error formatting and reporting is not\n      // necessary here; the errors are encoded in the stream, and will be\n      // reported in the \"Server\" environment.\n      onError: (error) => {\n        const digest = getDigestForWellKnownError(error)\n\n        if (digest) {\n          return digest\n        }\n\n        // TODO: For now we're also reporting the error here, because in\n        // production, the \"Server\" environment will only get the obfuscated\n        // error (created by the Flight Client in the cache wrapper).\n        console.error(error)\n        errors.push(error)\n      },\n    }\n  )\n\n  const [returnStream, savedStream] = stream.tee()\n\n  const promiseOfCacheEntry = collectResult(\n    savedStream,\n    outerWorkUnitStore,\n    innerCacheStore,\n    startTime,\n    errors,\n    timer\n  )\n\n  // Return the stream as we're creating it. This means that if it ends up\n  // erroring we cannot return a stale-while-error version but it allows\n  // streaming back the result earlier.\n  return [returnStream, promiseOfCacheEntry]\n}\n\nfunction cloneCacheEntry(entry: CacheEntry): [CacheEntry, CacheEntry] {\n  const [streamA, streamB] = entry.value.tee()\n  entry.value = streamA\n  const clonedEntry: CacheEntry = {\n    value: streamB,\n    timestamp: entry.timestamp,\n    revalidate: entry.revalidate,\n    expire: entry.expire,\n    stale: entry.stale,\n    tags: entry.tags,\n  }\n  return [entry, clonedEntry]\n}\n\nasync function clonePendingCacheEntry(\n  pendingCacheEntry: Promise<CacheEntry>\n): Promise<[CacheEntry, CacheEntry]> {\n  const entry = await pendingCacheEntry\n  return cloneCacheEntry(entry)\n}\n\nasync function getNthCacheEntry(\n  split: Promise<[CacheEntry, CacheEntry]>,\n  i: number\n): Promise<CacheEntry> {\n  return (await split)[i]\n}\n\nasync function encodeFormData(formData: FormData): Promise<string> {\n  let result = ''\n  for (let [key, value] of formData) {\n    // We don't need this key to be serializable but from a security perspective it should not be\n    // possible to generate a string that looks the same from a different structure. To ensure this\n    // we need a delimeter between fields but just using a delimeter is not enough since a string\n    // might contain that delimeter. We use the length of each field as the delimeter to avoid\n    // escaping the values.\n    result += key.length.toString(16) + ':' + key\n    let stringValue\n    if (typeof value === 'string') {\n      stringValue = value\n    } else {\n      // The FormData might contain binary data that is not valid UTF-8 so this cache\n      // key may generate a UCS-2 string. Passing this to another service needs to be\n      // aware that the key might not be compatible.\n      const arrayBuffer = await value.arrayBuffer()\n      if (arrayBuffer.byteLength % 2 === 0) {\n        stringValue = String.fromCodePoint(...new Uint16Array(arrayBuffer))\n      } else {\n        stringValue =\n          String.fromCodePoint(\n            ...new Uint16Array(arrayBuffer, 0, (arrayBuffer.byteLength - 1) / 2)\n          ) +\n          String.fromCodePoint(\n            new Uint8Array(arrayBuffer, arrayBuffer.byteLength - 1, 1)[0]\n          )\n      }\n    }\n    result += stringValue.length.toString(16) + ':' + stringValue\n  }\n  return result\n}\n\nfunction createTrackedReadableStream(\n  stream: ReadableStream,\n  cacheSignal: CacheSignal\n) {\n  const reader = stream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      const { done, value } = await reader.read()\n      if (done) {\n        controller.close()\n        cacheSignal.endRead()\n      } else {\n        controller.enqueue(value)\n      }\n    },\n  })\n}\n\nexport function cache(\n  kind: string,\n  id: string,\n  boundArgsLength: number,\n  fn: any\n) {\n  for (const [key, value] of Object.entries(\n    _globalThis.__nextCacheHandlers || {}\n  )) {\n    cacheHandlerMap.set(key, value as CacheHandler)\n  }\n  const cacheHandler = cacheHandlerMap.get(kind)\n\n  if (cacheHandler === undefined) {\n    throw new Error('Unknown cache handler: ' + kind)\n  }\n  const name = fn.name\n  const cachedFn = {\n    [name]: async function (...args: any[]) {\n      const workStore = workAsyncStorage.getStore()\n      if (workStore === undefined) {\n        throw new Error(\n          '\"use cache\" cannot be used outside of App Router. Expected a WorkStore.'\n        )\n      }\n\n      const workUnitStore = workUnitAsyncStorage.getStore()\n\n      // Get the clientReferenceManifest while we're still in the outer Context.\n      // In case getClientReferenceManifestSingleton is implemented using AsyncLocalStorage.\n      const clientReferenceManifest = getClientReferenceManifestForRsc()\n\n      // Because the Action ID is not yet unique per implementation of that Action we can't\n      // safely reuse the results across builds yet. In the meantime we add the buildId to the\n      // arguments as a seed to ensure they're not reused. Remove this once Action IDs hash\n      // the implementation.\n      const buildId = workStore.buildId\n\n      let abortHangingInputSignal: null | AbortSignal = null\n      if (workUnitStore && workUnitStore.type === 'prerender') {\n        // In a prerender, we may end up with hanging Promises as inputs due them stalling\n        // on connection() or because they're loading dynamic data. In that case we need to\n        // abort the encoding of the arguments since they'll never complete.\n        const controller = new AbortController()\n        abortHangingInputSignal = controller.signal\n        if (workUnitStore.cacheSignal) {\n          // If we have a cacheSignal it means we're in a prospective render. If the input\n          // we're waiting on is coming from another cache, we do want to wait for it so that\n          // we can resolve this cache entry too.\n          workUnitStore.cacheSignal.inputReady().then(() => {\n            controller.abort()\n          })\n        } else {\n          // Otherwise we're in the final render and we should already have all our caches\n          // filled. We might still be waiting on some microtasks so we wait one tick before\n          // giving up. When we give up, we still want to render the content of this cache\n          // as deeply as we can so that we can suspend as deeply as possible in the tree\n          // or not at all if we don't end up waiting for the input.\n          process.nextTick(() => controller.abort())\n        }\n      }\n\n      if (boundArgsLength > 0) {\n        if (args.length === 0) {\n          throw new InvariantError(\n            `Expected the \"use cache\" function ${JSON.stringify(fn.name)} to receive its encrypted bound arguments as the first argument.`\n          )\n        }\n\n        const encryptedBoundArgs = args.shift()\n        const boundArgs = await decryptActionBoundArgs(id, encryptedBoundArgs)\n\n        if (!Array.isArray(boundArgs)) {\n          throw new InvariantError(\n            `Expected the bound arguments of \"use cache\" function ${JSON.stringify(fn.name)} to deserialize into an array, got ${typeof boundArgs} instead.`\n          )\n        }\n\n        if (boundArgsLength !== boundArgs.length) {\n          throw new InvariantError(\n            `Expected the \"use cache\" function ${JSON.stringify(fn.name)} to receive ${boundArgsLength} bound arguments, got ${boundArgs.length} instead.`\n          )\n        }\n\n        args.unshift(boundArgs)\n      }\n\n      const temporaryReferences = createClientTemporaryReferenceSet()\n      const encodedArguments: FormData | string = await encodeReply(\n        [buildId, id, args],\n        // Right now this is enough to cause the input to generate hanging Promises\n        // but that's really due to what is probably a React bug in decodeReply.\n        // If that's fixed we may need a different strategy. We can also just skip\n        // the serialization/cache in this scenario and pass-through raw objects.\n        abortHangingInputSignal\n          ? {\n              temporaryReferences,\n              signal: abortHangingInputSignal,\n            }\n          : {\n              temporaryReferences,\n            }\n      )\n\n      const serializedCacheKey =\n        typeof encodedArguments === 'string'\n          ? // Fast path for the simple case for simple inputs. We let the CacheHandler\n            // Convert it to an ArrayBuffer if it wants to.\n            encodedArguments\n          : await encodeFormData(encodedArguments)\n\n      let stream: undefined | ReadableStream = undefined\n\n      // Get an immutable and mutable versions of the resume data cache.\n      const prerenderResumeDataCache = workUnitStore\n        ? getPrerenderResumeDataCache(workUnitStore)\n        : null\n      const renderResumeDataCache = workUnitStore\n        ? getRenderResumeDataCache(workUnitStore)\n        : null\n\n      if (renderResumeDataCache) {\n        const cacheSignal =\n          workUnitStore && workUnitStore.type === 'prerender'\n            ? workUnitStore.cacheSignal\n            : null\n\n        if (cacheSignal) {\n          cacheSignal.beginRead()\n        }\n        const cachedEntry = renderResumeDataCache.cache.get(serializedCacheKey)\n        if (cachedEntry !== undefined) {\n          const existingEntry = await cachedEntry\n          propagateCacheLifeAndTags(workUnitStore, existingEntry)\n          if (\n            workUnitStore !== undefined &&\n            workUnitStore.type === 'prerender' &&\n            existingEntry !== undefined &&\n            (existingEntry.revalidate === 0 ||\n              existingEntry.expire < DYNAMIC_EXPIRE)\n          ) {\n            // In a Dynamic I/O prerender, if the cache entry has revalidate: 0 or if the\n            // expire time is under 5 minutes, then we consider this cache entry dynamic\n            // as it's not worth generating static pages for such data. It's better to leave\n            // a PPR hole that can be filled in dynamically with a potentially cached entry.\n            if (cacheSignal) {\n              cacheSignal.endRead()\n            }\n            return makeHangingPromise(\n              workUnitStore.renderSignal,\n              'dynamic \"use cache\"'\n            )\n          }\n          const [streamA, streamB] = existingEntry.value.tee()\n          existingEntry.value = streamB\n\n          if (cacheSignal) {\n            // When we have a cacheSignal we need to block on reading the cache\n            // entry before ending the read.\n            stream = createTrackedReadableStream(streamA, cacheSignal)\n          } else {\n            stream = streamA\n          }\n        } else {\n          if (cacheSignal) {\n            cacheSignal.endRead()\n          }\n        }\n      }\n\n      if (stream === undefined) {\n        const cacheSignal =\n          workUnitStore && workUnitStore.type === 'prerender'\n            ? workUnitStore.cacheSignal\n            : null\n        if (cacheSignal) {\n          // Either the cache handler or the generation can be using I/O at this point.\n          // We need to track when they start and when they complete.\n          cacheSignal.beginRead()\n        }\n\n        const implicitTags =\n          workUnitStore === undefined || workUnitStore.type === 'unstable-cache'\n            ? []\n            : workUnitStore.implicitTags\n        const entry: undefined | CacheEntry = await cacheHandler.get(\n          serializedCacheKey,\n          implicitTags\n        )\n        const currentTime = performance.timeOrigin + performance.now()\n        if (\n          workUnitStore !== undefined &&\n          workUnitStore.type === 'prerender' &&\n          entry !== undefined &&\n          (entry.revalidate === 0 || entry.expire < DYNAMIC_EXPIRE)\n        ) {\n          // In a Dynamic I/O prerender, if the cache entry has revalidate: 0 or if the\n          // expire time is under 5 minutes, then we consider this cache entry dynamic\n          // as it's not worth generating static pages for such data. It's better to leave\n          // a PPR hole that can be filled in dynamically with a potentially cached entry.\n          if (cacheSignal) {\n            cacheSignal.endRead()\n          }\n\n          return makeHangingPromise(\n            workUnitStore.renderSignal,\n            'dynamic \"use cache\"'\n          )\n        } else if (\n          entry === undefined ||\n          currentTime > entry.timestamp + entry.expire * 1000 ||\n          (workStore.isStaticGeneration &&\n            currentTime > entry.timestamp + entry.revalidate * 1000)\n        ) {\n          // Miss. Generate a new result.\n\n          // If the cache entry is stale and we're prerendering, we don't want to use the\n          // stale entry since it would unnecessarily need to shorten the lifetime of the\n          // prerender. We're not time constrained here so we can re-generated it now.\n\n          // We need to run this inside a clean AsyncLocalStorage snapshot so that the cache\n          // generation cannot read anything from the context we're currently executing which\n          // might include request specific things like cookies() inside a React.cache().\n          // Note: It is important that we await at least once before this because it lets us\n          // pop out of any stack specific contexts as well - aka \"Sync\" Local Storage.\n\n          const [newStream, pendingCacheEntry] = await generateCacheEntry(\n            workStore,\n            workUnitStore,\n            clientReferenceManifest,\n            encodedArguments,\n            fn\n          )\n\n          let savedCacheEntry\n          if (prerenderResumeDataCache) {\n            // Create a clone that goes into the cache scope memory cache.\n            const split = clonePendingCacheEntry(pendingCacheEntry)\n            savedCacheEntry = getNthCacheEntry(split, 0)\n            prerenderResumeDataCache.cache.set(\n              serializedCacheKey,\n              getNthCacheEntry(split, 1)\n            )\n          } else {\n            savedCacheEntry = pendingCacheEntry\n          }\n\n          const promise = cacheHandler.set(serializedCacheKey, savedCacheEntry)\n\n          if (!workStore.pendingRevalidateWrites) {\n            workStore.pendingRevalidateWrites = []\n          }\n          workStore.pendingRevalidateWrites.push(promise)\n\n          stream = newStream\n        } else {\n          propagateCacheLifeAndTags(workUnitStore, entry)\n\n          // We want to return this stream, even if it's stale.\n          stream = entry.value\n\n          // If we have a cache scope, we need to clone the entry and set it on\n          // the inner cache scope.\n          if (prerenderResumeDataCache) {\n            const [entryLeft, entryRight] = cloneCacheEntry(entry)\n            if (cacheSignal) {\n              stream = createTrackedReadableStream(entryLeft.value, cacheSignal)\n            } else {\n              stream = entryLeft.value\n            }\n\n            prerenderResumeDataCache.cache.set(\n              serializedCacheKey,\n              Promise.resolve(entryRight)\n            )\n          } else {\n            // If we're not regenerating we need to signal that we've finished\n            // putting the entry into the cache scope at this point. Otherwise we do\n            // that inside generateCacheEntry.\n            cacheSignal?.endRead()\n          }\n\n          if (currentTime > entry.timestamp + entry.revalidate * 1000) {\n            // If this is stale, and we're not in a prerender (i.e. this is dynamic render),\n            // then we should warm up the cache with a fresh revalidated entry.\n            const [ignoredStream, pendingCacheEntry] = await generateCacheEntry(\n              workStore,\n              undefined, // This is not running within the context of this unit.\n              clientReferenceManifest,\n              encodedArguments,\n              fn\n            )\n\n            let savedCacheEntry: Promise<CacheEntry>\n            if (prerenderResumeDataCache) {\n              const split = clonePendingCacheEntry(pendingCacheEntry)\n              savedCacheEntry = getNthCacheEntry(split, 0)\n              prerenderResumeDataCache.cache.set(\n                serializedCacheKey,\n                getNthCacheEntry(split, 1)\n              )\n            } else {\n              savedCacheEntry = pendingCacheEntry\n            }\n\n            const promise = cacheHandler.set(\n              serializedCacheKey,\n              savedCacheEntry\n            )\n\n            if (!workStore.pendingRevalidateWrites) {\n              workStore.pendingRevalidateWrites = []\n            }\n            workStore.pendingRevalidateWrites.push(promise)\n\n            await ignoredStream.cancel()\n          }\n        }\n      }\n\n      // Logs are replayed even if it's a hit - to ensure we see them on the client eventually.\n      // If we didn't then the client wouldn't see the logs if it was seeded from a prewarm that\n      // never made it to the client. However, this also means that you see logs even when the\n      // cached function isn't actually re-executed. We should instead ensure prewarms always\n      // make it to the client. Another issue is that this will cause double logging in the\n      // server terminal. Once while generating the cache entry and once when replaying it on\n      // the server, which is required to pick it up for replaying again on the client.\n      const replayConsoleLogs = true\n\n      const serverConsumerManifest = {\n        // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n        // to be added to the consumer. Instead, we'll wait for any ClientReference to be emitted\n        // which themselves will handle the preloading.\n        moduleLoading: null,\n        moduleMap: isEdgeRuntime\n          ? clientReferenceManifest.edgeRscModuleMapping\n          : clientReferenceManifest.rscModuleMapping,\n        serverModuleMap: getServerModuleMap(),\n      }\n\n      return createFromReadableStream(stream, {\n        serverConsumerManifest,\n        temporaryReferences,\n        replayConsoleLogs,\n        environmentName: 'Cache',\n      })\n    },\n  }[name]\n  return cachedFn\n}\n"], "names": ["cache", "_globalThis", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "DYNAMIC_EXPIRE", "cacheHandlersSymbol", "Symbol", "for", "globalThis", "cacheHandlerMap", "Map", "DefaultCache", "DefaultCache<PERSON>andler", "RemoteCache", "generateCacheEntry", "workStore", "outerWorkUnitStore", "clientReferenceManifest", "encodedArguments", "fn", "runInCleanSnapshot", "generateCacheEntryWithRestoredWorkStore", "workAsyncStorage", "run", "generateCacheEntryWithCacheContext", "cacheLifeProfiles", "Error", "defaultCacheLife", "revalidate", "expire", "stale", "cacheStore", "type", "phase", "implicitTags", "undefined", "explicitRevalidate", "explicitExpire", "explicitStale", "tags", "workUnitAsyncStorage", "generateCacheEntryImpl", "propagateCacheLifeAndTags", "workUnitStore", "entry", "outerTags", "entryTags", "i", "length", "tag", "includes", "push", "collectResult", "savedStream", "innerCacheStore", "startTime", "errors", "timer", "buffer", "reader", "<PERSON><PERSON><PERSON><PERSON>", "read", "done", "value", "idx", "bufferStream", "ReadableStream", "pull", "controller", "enqueue", "error", "close", "collectedTags", "collectedRevalidate", "collectedExpire", "collectedStale", "timestamp", "cacheSignal", "endRead", "clearTimeout", "temporaryReferences", "createServerTemporaryReferenceSet", "args", "decodeReply", "getServerModuleMap", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "result", "apply", "AbortController", "isStaticGeneration", "setTimeout", "abort", "stream", "renderToReadableStream", "clientModules", "environmentName", "signal", "onError", "digest", "getDigestForWellKnownError", "console", "returnStream", "tee", "promiseOfCacheEntry", "cloneCacheEntry", "streamA", "streamB", "clonedEntry", "clonePendingCacheEntry", "pendingCacheEntry", "getNthCacheEntry", "split", "encodeFormData", "formData", "key", "toString", "stringValue", "arrayBuffer", "byteLength", "String", "fromCodePoint", "Uint16Array", "Uint8Array", "createTrackedReadableStream", "kind", "id", "boundArgs<PERSON><PERSON>th", "Object", "entries", "__next<PERSON>ache<PERSON>and<PERSON>", "set", "cache<PERSON><PERSON><PERSON>", "get", "name", "cachedFn", "getStore", "getClientReferenceManifestForRsc", "buildId", "abortHangingInputSignal", "inputReady", "then", "nextTick", "InvariantError", "JSON", "stringify", "encryptedBoundArgs", "shift", "boundArgs", "decryptActionBoundArgs", "Array", "isArray", "unshift", "createClientTemporaryReferenceSet", "encodeReply", "serialized<PERSON>ache<PERSON>ey", "prerenderResumeDataCache", "getPrerenderResumeDataCache", "renderResumeDataCache", "getRenderResumeDataCache", "beginRead", "cachedEntry", "existingEntry", "makeHangingPromise", "renderSignal", "currentTime", "newStream", "savedCacheEntry", "promise", "pendingRevalidateWrites", "entryLeft", "entryRight", "Promise", "resolve", "ignoredStream", "cancel", "replayConsoleLogs", "serverConsumerManifest", "moduleLoading", "moduleMap", "edgeRscModuleMapping", "rscModuleMapping", "serverModuleMap", "createFromReadableStream"], "mappings": ";;;;+BAkcgBA;;;eAAAA;;;4BA5bT;4BAMA;0CAG0B;8CAS1B;4CAC4B;uCAEA;iCAO5B;gEACyB;4BAGO;gCACR;oCACY;;;;;;IAmBvCC,iCAIA,wCAAwC;AACxC,wCAAwC;AACxCA;AAvBJ,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,oCAAoC;AACpC,MAAMC,iBAAiB;AAEvB,MAAMC,sBAAsBC,OAAOC,GAAG,CAAC;AACvC,MAAMR,cAMFS;AAEJ,MAAMC,kBAA6C,IAAIC,IAAI;IACzD;QACE;QACAX,EAAAA,kCAAAA,WAAW,CAACM,oBAAoB,qBAAhCN,gCAAkCY,YAAY,KAAIC,gBAAmB;KACtE;IACD;QACE;QAGAb,EAAAA,mCAAAA,WAAW,CAACM,oBAAoB,qBAAhCN,iCAAkCc,WAAW,KAAID,gBAAmB;KACrE;CACF;AAED,SAASE,mBACPC,SAAoB,EACpBC,kBAA6C,EAC7CC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAAO;IAEP,kFAAkF;IAClF,mFAAmF;IACnF,+EAA+E;IAC/E,mFAAmF;IACnF,6EAA6E;IAC7E,OAAOC,IAAAA,8CAAkB,EACvBC,yCACAN,WACAC,oBACAC,yBACAC,kBACAC;AAEJ;AAEA,SAASE,wCACPN,SAAoB,EACpBC,kBAA6C,EAC7CC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAAO;IAEP,2EAA2E;IAC3E,6EAA6E;IAC7E,sFAAsF;IACtF,sFAAsF;IACtF,+EAA+E;IAC/E,sFAAsF;IACtF,0DAA0D;IAC1D,OAAOG,0CAAgB,CAACC,GAAG,CACzBR,WACAS,oCACAT,WACAC,oBACAC,yBACAC,kBACAC;AAEJ;AAEA,SAASK,mCACPT,SAAoB,EACpBC,kBAA6C,EAC7CC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAAO;IAEP,IAAI,CAACJ,UAAUU,iBAAiB,EAAE;QAChC,MAAM,IAAIC,MACR;IAEJ;IACA,MAAMC,mBAAmBZ,UAAUU,iBAAiB,CAAC,UAAU;IAC/D,IACE,CAACE,oBACDA,iBAAiBC,UAAU,IAAI,QAC/BD,iBAAiBE,MAAM,IAAI,QAC3BF,iBAAiBG,KAAK,IAAI,MAC1B;QACA,MAAM,IAAIJ,MACR;IAEJ;IAEA,6CAA6C;IAC7C,MAAMK,aAA4B;QAChCC,MAAM;QACNC,OAAO;QACPC,cACElB,uBAAuBmB,aACvBnB,mBAAmBgB,IAAI,KAAK,mBACxB,EAAE,GACFhB,mBAAmBkB,YAAY;QACrCN,YAAYD,iBAAiBC,UAAU;QACvCC,QAAQF,iBAAiBE,MAAM;QAC/BC,OAAOH,iBAAiBG,KAAK;QAC7BM,oBAAoBD;QACpBE,gBAAgBF;QAChBG,eAAeH;QACfI,MAAM;IACR;IACA,OAAOC,kDAAoB,CAACjB,GAAG,CAC7BQ,YACAU,wBACA1B,WACAC,oBACAe,YACAd,yBACAC,kBACAC;AAEJ;AAEA,SAASuB,0BACPC,aAAwC,EACxCC,KAAiB;IAEjB,IACED,iBACCA,CAAAA,cAAcX,IAAI,KAAK,WACtBW,cAAcX,IAAI,KAAK,eACvBW,cAAcX,IAAI,KAAK,mBACvBW,cAAcX,IAAI,KAAK,kBAAiB,GAC1C;QACA,wCAAwC;QACxC,MAAMa,YAAYF,cAAcJ,IAAI,IAAKI,CAAAA,cAAcJ,IAAI,GAAG,EAAE,AAAD;QAC/D,MAAMO,YAAYF,MAAML,IAAI;QAC5B,IAAK,IAAIQ,IAAI,GAAGA,IAAID,UAAUE,MAAM,EAAED,IAAK;YACzC,MAAME,MAAMH,SAAS,CAACC,EAAE;YACxB,IAAI,CAACF,UAAUK,QAAQ,CAACD,MAAM;gBAC5BJ,UAAUM,IAAI,CAACF;YACjB;QACF;QACA,IAAIN,cAAcb,KAAK,GAAGc,MAAMd,KAAK,EAAE;YACrCa,cAAcb,KAAK,GAAGc,MAAMd,KAAK;QACnC;QACA,IAAIa,cAAcf,UAAU,GAAGgB,MAAMhB,UAAU,EAAE;YAC/Ce,cAAcf,UAAU,GAAGgB,MAAMhB,UAAU;QAC7C;QACA,IAAIe,cAAcd,MAAM,GAAGe,MAAMf,MAAM,EAAE;YACvCc,cAAcd,MAAM,GAAGe,MAAMf,MAAM;QACrC;IACF;AACF;AAEA,eAAeuB,cACbC,WAA2B,EAC3BrC,kBAA6C,EAC7CsC,eAA8B,EAC9BC,SAAiB,EACjBC,MAAsB,EACtBC,KAAU;IAEV,wEAAwE;IACxE,yEAAyE;IACzE,wEAAwE;IACxE,mDAAmD;IACnD,EAAE;IACF,oEAAoE;IACpE,qEAAqE;IACrE,0EAA0E;IAC1E,wEAAwE;IACxE,6EAA6E;IAC7E,2EAA2E;IAC3E,cAAc;IAEd,MAAMC,SAAgB,EAAE;IACxB,MAAMC,SAASN,YAAYO,SAAS;IACpC,IAAK,IAAIhB,OAAO,CAAC,AAACA,CAAAA,QAAQ,MAAMe,OAAOE,IAAI,EAAC,EAAGC,IAAI,EAAI;QACrDJ,OAAOP,IAAI,CAACP,MAAMmB,KAAK;IACzB;IAEA,IAAIC,MAAM;IACV,MAAMC,eAAe,IAAIC,eAAe;QACtCC,MAAKC,UAAU;YACb,IAAIJ,MAAMN,OAAOV,MAAM,EAAE;gBACvBoB,WAAWC,OAAO,CAACX,MAAM,CAACM,MAAM;YAClC,OAAO,IAAIR,OAAOR,MAAM,GAAG,GAAG;gBAC5B,2CAA2C;gBAC3CoB,WAAWE,KAAK,CAACd,MAAM,CAAC,EAAE;YAC5B,OAAO;gBACLY,WAAWG,KAAK;YAClB;QACF;IACF;IAEA,MAAMC,gBAAgBlB,gBAAgBf,IAAI;IAC1C,0EAA0E;IAC1E,4FAA4F;IAC5F,qCAAqC;IACrC,MAAMkC,sBACJnB,gBAAgBlB,kBAAkB,KAAKD,YACnCmB,gBAAgBlB,kBAAkB,GAClCkB,gBAAgB1B,UAAU;IAChC,MAAM8C,kBACJpB,gBAAgBjB,cAAc,KAAKF,YAC/BmB,gBAAgBjB,cAAc,GAC9BiB,gBAAgBzB,MAAM;IAC5B,MAAM8C,iBACJrB,gBAAgBhB,aAAa,KAAKH,YAC9BmB,gBAAgBhB,aAAa,GAC7BgB,gBAAgBxB,KAAK;IAE3B,MAAMc,QAAQ;QACZmB,OAAOE;QACPW,WAAWrB;QACX3B,YAAY6C;QACZ5C,QAAQ6C;QACR5C,OAAO6C;QACPpC,MAAMiC,kBAAkB,OAAO,EAAE,GAAGA;IACtC;IACA,mDAAmD;IACnD9B,0BAA0B1B,oBAAoB4B;IAE9C,MAAMiC,cACJ7D,sBAAsBA,mBAAmBgB,IAAI,KAAK,cAC9ChB,mBAAmB6D,WAAW,GAC9B;IACN,IAAIA,aAAa;QACfA,YAAYC,OAAO;IACrB;IAEA,IAAIrB,UAAUtB,WAAW;QACvB4C,aAAatB;IACf;IAEA,OAAOb;AACT;AAEA,eAAeH,uBACb1B,SAAoB,EACpBC,kBAA6C,EAC7CsC,eAA8B,EAC9BrC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAAO;IAEP,MAAM6D,sBAAsBC,IAAAA,uCAAiC;IAE7D,MAAM,KAAKC,KAAK,GAAG,MAAMC,IAAAA,uBAAW,EAClCjE,kBACAkE,IAAAA,mCAAkB,KAClB;QACEJ;IACF;IAGF,4DAA4D;IAC5D,MAAMzB,YAAY8B,YAAYC,UAAU,GAAGD,YAAYE,GAAG;IAC1D,kDAAkD;IAClD,MAAMC,SAASrE,GAAGsE,KAAK,CAAC,MAAMP;IAE9B,IAAI1B,SAAyB,EAAE;IAE/B,IAAIC,QAAQtB;IACZ,MAAMiC,aAAa,IAAIsB;IACvB,IAAI3E,UAAU4E,kBAAkB,EAAE;QAChC,iFAAiF;QACjF,gFAAgF;QAChF,0CAA0C;QAC1ClC,QAAQmC,WAAW;YACjBxB,WAAWyB,KAAK,CACd,IAAInE,MACF,mGACE;QAGR,GAAG;IACL;IAEA,MAAMoE,SAASC,IAAAA,kCAAsB,EACnCP,QACAvE,wBAAwB+E,aAAa,EACrC;QACEC,iBAAiB;QACjBC,QAAQ9B,WAAW8B,MAAM;QACzBlB;QACA,uEAAuE;QACvE,uEAAuE;QACvE,oEAAoE;QACpE,wCAAwC;QACxCmB,SAAS,CAAC7B;YACR,MAAM8B,SAASC,IAAAA,8CAA0B,EAAC/B;YAE1C,IAAI8B,QAAQ;gBACV,OAAOA;YACT;YAEA,gEAAgE;YAChE,oEAAoE;YACpE,6DAA6D;YAC7DE,QAAQhC,KAAK,CAACA;YACdd,OAAOL,IAAI,CAACmB;QACd;IACF;IAGF,MAAM,CAACiC,cAAclD,YAAY,GAAGyC,OAAOU,GAAG;IAE9C,MAAMC,sBAAsBrD,cAC1BC,aACArC,oBACAsC,iBACAC,WACAC,QACAC;IAGF,wEAAwE;IACxE,sEAAsE;IACtE,qCAAqC;IACrC,OAAO;QAAC8C;QAAcE;KAAoB;AAC5C;AAEA,SAASC,gBAAgB9D,KAAiB;IACxC,MAAM,CAAC+D,SAASC,QAAQ,GAAGhE,MAAMmB,KAAK,CAACyC,GAAG;IAC1C5D,MAAMmB,KAAK,GAAG4C;IACd,MAAME,cAA0B;QAC9B9C,OAAO6C;QACPhC,WAAWhC,MAAMgC,SAAS;QAC1BhD,YAAYgB,MAAMhB,UAAU;QAC5BC,QAAQe,MAAMf,MAAM;QACpBC,OAAOc,MAAMd,KAAK;QAClBS,MAAMK,MAAML,IAAI;IAClB;IACA,OAAO;QAACK;QAAOiE;KAAY;AAC7B;AAEA,eAAeC,uBACbC,iBAAsC;IAEtC,MAAMnE,QAAQ,MAAMmE;IACpB,OAAOL,gBAAgB9D;AACzB;AAEA,eAAeoE,iBACbC,KAAwC,EACxClE,CAAS;IAET,OAAO,AAAC,CAAA,MAAMkE,KAAI,CAAE,CAAClE,EAAE;AACzB;AAEA,eAAemE,eAAeC,QAAkB;IAC9C,IAAI3B,SAAS;IACb,KAAK,IAAI,CAAC4B,KAAKrD,MAAM,IAAIoD,SAAU;QACjC,6FAA6F;QAC7F,+FAA+F;QAC/F,6FAA6F;QAC7F,0FAA0F;QAC1F,uBAAuB;QACvB3B,UAAU4B,IAAIpE,MAAM,CAACqE,QAAQ,CAAC,MAAM,MAAMD;QAC1C,IAAIE;QACJ,IAAI,OAAOvD,UAAU,UAAU;YAC7BuD,cAAcvD;QAChB,OAAO;YACL,+EAA+E;YAC/E,+EAA+E;YAC/E,8CAA8C;YAC9C,MAAMwD,cAAc,MAAMxD,MAAMwD,WAAW;YAC3C,IAAIA,YAAYC,UAAU,GAAG,MAAM,GAAG;gBACpCF,cAAcG,OAAOC,aAAa,IAAI,IAAIC,YAAYJ;YACxD,OAAO;gBACLD,cACEG,OAAOC,aAAa,IACf,IAAIC,YAAYJ,aAAa,GAAG,AAACA,CAAAA,YAAYC,UAAU,GAAG,CAAA,IAAK,MAEpEC,OAAOC,aAAa,CAClB,IAAIE,WAAWL,aAAaA,YAAYC,UAAU,GAAG,GAAG,EAAE,CAAC,EAAE;YAEnE;QACF;QACAhC,UAAU8B,YAAYtE,MAAM,CAACqE,QAAQ,CAAC,MAAM,MAAMC;IACpD;IACA,OAAO9B;AACT;AAEA,SAASqC,4BACP/B,MAAsB,EACtBjB,WAAwB;IAExB,MAAMlB,SAASmC,OAAOlC,SAAS;IAC/B,OAAO,IAAIM,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,MAAM,EAAEN,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOE,IAAI;YACzC,IAAIC,MAAM;gBACRM,WAAWG,KAAK;gBAChBM,YAAYC,OAAO;YACrB,OAAO;gBACLV,WAAWC,OAAO,CAACN;YACrB;QACF;IACF;AACF;AAEO,SAASjE,MACdgI,IAAY,EACZC,EAAU,EACVC,eAAuB,EACvB7G,EAAO;IAEP,KAAK,MAAM,CAACiG,KAAKrD,MAAM,IAAIkE,OAAOC,OAAO,CACvCnI,YAAYoI,mBAAmB,IAAI,CAAC,GACnC;QACD1H,gBAAgB2H,GAAG,CAAChB,KAAKrD;IAC3B;IACA,MAAMsE,eAAe5H,gBAAgB6H,GAAG,CAACR;IAEzC,IAAIO,iBAAiBlG,WAAW;QAC9B,MAAM,IAAIT,MAAM,4BAA4BoG;IAC9C;IACA,MAAMS,OAAOpH,GAAGoH,IAAI;IACpB,MAAMC,WAAW;QACf,CAACD,KAAK,EAAE,eAAgB,GAAGrD,IAAW;YACpC,MAAMnE,YAAYO,0CAAgB,CAACmH,QAAQ;YAC3C,IAAI1H,cAAcoB,WAAW;gBAC3B,MAAM,IAAIT,MACR;YAEJ;YAEA,MAAMiB,gBAAgBH,kDAAoB,CAACiG,QAAQ;YAEnD,0EAA0E;YAC1E,sFAAsF;YACtF,MAAMxH,0BAA0ByH,IAAAA,iDAAgC;YAEhE,qFAAqF;YACrF,wFAAwF;YACxF,qFAAqF;YACrF,sBAAsB;YACtB,MAAMC,UAAU5H,UAAU4H,OAAO;YAEjC,IAAIC,0BAA8C;YAClD,IAAIjG,iBAAiBA,cAAcX,IAAI,KAAK,aAAa;gBACvD,kFAAkF;gBAClF,mFAAmF;gBACnF,oEAAoE;gBACpE,MAAMoC,aAAa,IAAIsB;gBACvBkD,0BAA0BxE,WAAW8B,MAAM;gBAC3C,IAAIvD,cAAckC,WAAW,EAAE;oBAC7B,gFAAgF;oBAChF,mFAAmF;oBACnF,uCAAuC;oBACvClC,cAAckC,WAAW,CAACgE,UAAU,GAAGC,IAAI,CAAC;wBAC1C1E,WAAWyB,KAAK;oBAClB;gBACF,OAAO;oBACL,gFAAgF;oBAChF,kFAAkF;oBAClF,gFAAgF;oBAChF,+EAA+E;oBAC/E,0DAA0D;oBAC1D5F,QAAQ8I,QAAQ,CAAC,IAAM3E,WAAWyB,KAAK;gBACzC;YACF;YAEA,IAAImC,kBAAkB,GAAG;gBACvB,IAAI9C,KAAKlC,MAAM,KAAK,GAAG;oBACrB,MAAM,IAAIgG,8BAAc,CACtB,CAAC,kCAAkC,EAAEC,KAAKC,SAAS,CAAC/H,GAAGoH,IAAI,EAAE,gEAAgE,CAAC;gBAElI;gBAEA,MAAMY,qBAAqBjE,KAAKkE,KAAK;gBACrC,MAAMC,YAAY,MAAMC,IAAAA,kCAAsB,EAACvB,IAAIoB;gBAEnD,IAAI,CAACI,MAAMC,OAAO,CAACH,YAAY;oBAC7B,MAAM,IAAIL,8BAAc,CACtB,CAAC,qDAAqD,EAAEC,KAAKC,SAAS,CAAC/H,GAAGoH,IAAI,EAAE,mCAAmC,EAAE,OAAOc,UAAU,SAAS,CAAC;gBAEpJ;gBAEA,IAAIrB,oBAAoBqB,UAAUrG,MAAM,EAAE;oBACxC,MAAM,IAAIgG,8BAAc,CACtB,CAAC,kCAAkC,EAAEC,KAAKC,SAAS,CAAC/H,GAAGoH,IAAI,EAAE,YAAY,EAAEP,gBAAgB,sBAAsB,EAAEqB,UAAUrG,MAAM,CAAC,SAAS,CAAC;gBAElJ;gBAEAkC,KAAKuE,OAAO,CAACJ;YACf;YAEA,MAAMrE,sBAAsB0E,IAAAA,uCAAiC;YAC7D,MAAMxI,mBAAsC,MAAMyI,IAAAA,uBAAW,EAC3D;gBAAChB;gBAASZ;gBAAI7C;aAAK,EACnB,2EAA2E;YAC3E,wEAAwE;YACxE,0EAA0E;YAC1E,yEAAyE;YACzE0D,0BACI;gBACE5D;gBACAkB,QAAQ0C;YACV,IACA;gBACE5D;YACF;YAGN,MAAM4E,qBACJ,OAAO1I,qBAAqB,WAExB,+CAA+C;YAC/CA,mBACA,MAAMgG,eAAehG;YAE3B,IAAI4E,SAAqC3D;YAEzC,kEAAkE;YAClE,MAAM0H,2BAA2BlH,gBAC7BmH,IAAAA,yDAA2B,EAACnH,iBAC5B;YACJ,MAAMoH,wBAAwBpH,gBAC1BqH,IAAAA,sDAAwB,EAACrH,iBACzB;YAEJ,IAAIoH,uBAAuB;gBACzB,MAAMlF,cACJlC,iBAAiBA,cAAcX,IAAI,KAAK,cACpCW,cAAckC,WAAW,GACzB;gBAEN,IAAIA,aAAa;oBACfA,YAAYoF,SAAS;gBACvB;gBACA,MAAMC,cAAcH,sBAAsBjK,KAAK,CAACwI,GAAG,CAACsB;gBACpD,IAAIM,gBAAgB/H,WAAW;oBAC7B,MAAMgI,gBAAgB,MAAMD;oBAC5BxH,0BAA0BC,eAAewH;oBACzC,IACExH,kBAAkBR,aAClBQ,cAAcX,IAAI,KAAK,eACvBmI,kBAAkBhI,aACjBgI,CAAAA,cAAcvI,UAAU,KAAK,KAC5BuI,cAActI,MAAM,GAAGzB,cAAa,GACtC;wBACA,6EAA6E;wBAC7E,4EAA4E;wBAC5E,gFAAgF;wBAChF,gFAAgF;wBAChF,IAAIyE,aAAa;4BACfA,YAAYC,OAAO;wBACrB;wBACA,OAAOsF,IAAAA,yCAAkB,EACvBzH,cAAc0H,YAAY,EAC1B;oBAEJ;oBACA,MAAM,CAAC1D,SAASC,QAAQ,GAAGuD,cAAcpG,KAAK,CAACyC,GAAG;oBAClD2D,cAAcpG,KAAK,GAAG6C;oBAEtB,IAAI/B,aAAa;wBACf,mEAAmE;wBACnE,gCAAgC;wBAChCiB,SAAS+B,4BAA4BlB,SAAS9B;oBAChD,OAAO;wBACLiB,SAASa;oBACX;gBACF,OAAO;oBACL,IAAI9B,aAAa;wBACfA,YAAYC,OAAO;oBACrB;gBACF;YACF;YAEA,IAAIgB,WAAW3D,WAAW;gBACxB,MAAM0C,cACJlC,iBAAiBA,cAAcX,IAAI,KAAK,cACpCW,cAAckC,WAAW,GACzB;gBACN,IAAIA,aAAa;oBACf,6EAA6E;oBAC7E,2DAA2D;oBAC3DA,YAAYoF,SAAS;gBACvB;gBAEA,MAAM/H,eACJS,kBAAkBR,aAAaQ,cAAcX,IAAI,KAAK,mBAClD,EAAE,GACFW,cAAcT,YAAY;gBAChC,MAAMU,QAAgC,MAAMyF,aAAaC,GAAG,CAC1DsB,oBACA1H;gBAEF,MAAMoI,cAAcjF,YAAYC,UAAU,GAAGD,YAAYE,GAAG;gBAC5D,IACE5C,kBAAkBR,aAClBQ,cAAcX,IAAI,KAAK,eACvBY,UAAUT,aACTS,CAAAA,MAAMhB,UAAU,KAAK,KAAKgB,MAAMf,MAAM,GAAGzB,cAAa,GACvD;oBACA,6EAA6E;oBAC7E,4EAA4E;oBAC5E,gFAAgF;oBAChF,gFAAgF;oBAChF,IAAIyE,aAAa;wBACfA,YAAYC,OAAO;oBACrB;oBAEA,OAAOsF,IAAAA,yCAAkB,EACvBzH,cAAc0H,YAAY,EAC1B;gBAEJ,OAAO,IACLzH,UAAUT,aACVmI,cAAc1H,MAAMgC,SAAS,GAAGhC,MAAMf,MAAM,GAAG,QAC9Cd,UAAU4E,kBAAkB,IAC3B2E,cAAc1H,MAAMgC,SAAS,GAAGhC,MAAMhB,UAAU,GAAG,MACrD;oBACA,+BAA+B;oBAE/B,+EAA+E;oBAC/E,+EAA+E;oBAC/E,4EAA4E;oBAE5E,kFAAkF;oBAClF,mFAAmF;oBACnF,+EAA+E;oBAC/E,mFAAmF;oBACnF,6EAA6E;oBAE7E,MAAM,CAAC2I,WAAWxD,kBAAkB,GAAG,MAAMjG,mBAC3CC,WACA4B,eACA1B,yBACAC,kBACAC;oBAGF,IAAIqJ;oBACJ,IAAIX,0BAA0B;wBAC5B,8DAA8D;wBAC9D,MAAM5C,QAAQH,uBAAuBC;wBACrCyD,kBAAkBxD,iBAAiBC,OAAO;wBAC1C4C,yBAAyB/J,KAAK,CAACsI,GAAG,CAChCwB,oBACA5C,iBAAiBC,OAAO;oBAE5B,OAAO;wBACLuD,kBAAkBzD;oBACpB;oBAEA,MAAM0D,UAAUpC,aAAaD,GAAG,CAACwB,oBAAoBY;oBAErD,IAAI,CAACzJ,UAAU2J,uBAAuB,EAAE;wBACtC3J,UAAU2J,uBAAuB,GAAG,EAAE;oBACxC;oBACA3J,UAAU2J,uBAAuB,CAACvH,IAAI,CAACsH;oBAEvC3E,SAASyE;gBACX,OAAO;oBACL7H,0BAA0BC,eAAeC;oBAEzC,qDAAqD;oBACrDkD,SAASlD,MAAMmB,KAAK;oBAEpB,qEAAqE;oBACrE,yBAAyB;oBACzB,IAAI8F,0BAA0B;wBAC5B,MAAM,CAACc,WAAWC,WAAW,GAAGlE,gBAAgB9D;wBAChD,IAAIiC,aAAa;4BACfiB,SAAS+B,4BAA4B8C,UAAU5G,KAAK,EAAEc;wBACxD,OAAO;4BACLiB,SAAS6E,UAAU5G,KAAK;wBAC1B;wBAEA8F,yBAAyB/J,KAAK,CAACsI,GAAG,CAChCwB,oBACAiB,QAAQC,OAAO,CAACF;oBAEpB,OAAO;wBACL,kEAAkE;wBAClE,wEAAwE;wBACxE,kCAAkC;wBAClC/F,+BAAAA,YAAaC,OAAO;oBACtB;oBAEA,IAAIwF,cAAc1H,MAAMgC,SAAS,GAAGhC,MAAMhB,UAAU,GAAG,MAAM;wBAC3D,gFAAgF;wBAChF,mEAAmE;wBACnE,MAAM,CAACmJ,eAAehE,kBAAkB,GAAG,MAAMjG,mBAC/CC,WACAoB,WACAlB,yBACAC,kBACAC;wBAGF,IAAIqJ;wBACJ,IAAIX,0BAA0B;4BAC5B,MAAM5C,QAAQH,uBAAuBC;4BACrCyD,kBAAkBxD,iBAAiBC,OAAO;4BAC1C4C,yBAAyB/J,KAAK,CAACsI,GAAG,CAChCwB,oBACA5C,iBAAiBC,OAAO;wBAE5B,OAAO;4BACLuD,kBAAkBzD;wBACpB;wBAEA,MAAM0D,UAAUpC,aAAaD,GAAG,CAC9BwB,oBACAY;wBAGF,IAAI,CAACzJ,UAAU2J,uBAAuB,EAAE;4BACtC3J,UAAU2J,uBAAuB,GAAG,EAAE;wBACxC;wBACA3J,UAAU2J,uBAAuB,CAACvH,IAAI,CAACsH;wBAEvC,MAAMM,cAAcC,MAAM;oBAC5B;gBACF;YACF;YAEA,yFAAyF;YACzF,0FAA0F;YAC1F,wFAAwF;YACxF,uFAAuF;YACvF,qFAAqF;YACrF,uFAAuF;YACvF,iFAAiF;YACjF,MAAMC,oBAAoB;YAE1B,MAAMC,yBAAyB;gBAC7B,2FAA2F;gBAC3F,yFAAyF;gBACzF,+CAA+C;gBAC/CC,eAAe;gBACfC,WAAWpL,gBACPiB,wBAAwBoK,oBAAoB,GAC5CpK,wBAAwBqK,gBAAgB;gBAC5CC,iBAAiBnG,IAAAA,mCAAkB;YACrC;YAEA,OAAOoG,IAAAA,oCAAwB,EAAC1F,QAAQ;gBACtCoF;gBACAlG;gBACAiG;gBACAhF,iBAAiB;YACnB;QACF;IACF,CAAC,CAACsC,KAAK;IACP,OAAOC;AACT"}