{"version": 3, "sources": ["../../../src/server/web/web-on-close.ts"], "sourcesContent": ["/** Monitor when the consumer finishes reading the response body.\nthat's as close as we can get to `res.on('close')` using web APIs.\n*/\nexport function trackBodyConsumed(\n  body: string | ReadableStream,\n  onEnd: () => void\n): BodyInit {\n  if (typeof body === 'string') {\n    const generator = async function* generate() {\n      const encoder = new TextEncoder()\n      yield encoder.encode(body)\n      onEnd()\n    }\n    // @ts-expect-error BodyInit typings doesn't seem to include AsyncIterables even though it's supported in practice\n    return generator()\n  } else {\n    return trackStreamConsumed(body, onEnd)\n  }\n}\n\nexport function trackStreamConsumed<TChunk>(\n  stream: ReadableStream<TChunk>,\n  onEnd: () => void\n): ReadableStream<TChunk> {\n  const closePassThrough = new TransformStream<TChunk, TChunk>({\n    flush: () => {\n      return onEnd()\n    },\n  })\n  return stream.pipeThrough(closePassThrough)\n}\n\nexport class CloseController {\n  private target = new EventTarget()\n  listeners = 0\n  isClosed = false\n\n  onClose(callback: () => void) {\n    if (this.isClosed) {\n      throw new Error('Cannot subscribe to a closed CloseController')\n    }\n\n    this.target.addEventListener('close', callback)\n    this.listeners++\n  }\n\n  dispatchClose() {\n    if (this.isClosed) {\n      throw new Error('Cannot close a CloseController multiple times')\n    }\n    if (this.listeners > 0) {\n      this.target.dispatchEvent(new Event('close'))\n    }\n    this.isClosed = true\n  }\n}\n"], "names": ["CloseController", "trackBodyConsumed", "trackStreamConsumed", "body", "onEnd", "generator", "generate", "encoder", "TextEncoder", "encode", "stream", "closePassThrough", "TransformStream", "flush", "pipeThrough", "onClose", "callback", "isClosed", "Error", "target", "addEventListener", "listeners", "dispatchClose", "dispatchEvent", "Event", "EventTarget"], "mappings": "AAAA;;AAEA;;;;;;;;;;;;;;;;IA8BaA,eAAe;eAAfA;;IA7BGC,iBAAiB;eAAjBA;;IAiBAC,mBAAmB;eAAnBA;;;AAjBT,SAASD,kBACdE,IAA6B,EAC7BC,KAAiB;IAEjB,IAAI,OAAOD,SAAS,UAAU;QAC5B,MAAME,YAAY,gBAAgBC;YAChC,MAAMC,UAAU,IAAIC;YACpB,MAAMD,QAAQE,MAAM,CAACN;YACrBC;QACF;QACA,kHAAkH;QAClH,OAAOC;IACT,OAAO;QACL,OAAOH,oBAAoBC,MAAMC;IACnC;AACF;AAEO,SAASF,oBACdQ,MAA8B,EAC9BN,KAAiB;IAEjB,MAAMO,mBAAmB,IAAIC,gBAAgC;QAC3DC,OAAO;YACL,OAAOT;QACT;IACF;IACA,OAAOM,OAAOI,WAAW,CAACH;AAC5B;AAEO,MAAMX;IAKXe,QAAQC,QAAoB,EAAE;QAC5B,IAAI,IAAI,CAACC,QAAQ,EAAE;YACjB,MAAM,IAAIC,MAAM;QAClB;QAEA,IAAI,CAACC,MAAM,CAACC,gBAAgB,CAAC,SAASJ;QACtC,IAAI,CAACK,SAAS;IAChB;IAEAC,gBAAgB;QACd,IAAI,IAAI,CAACL,QAAQ,EAAE;YACjB,MAAM,IAAIC,MAAM;QAClB;QACA,IAAI,IAAI,CAACG,SAAS,GAAG,GAAG;YACtB,IAAI,CAACF,MAAM,CAACI,aAAa,CAAC,IAAIC,MAAM;QACtC;QACA,IAAI,CAACP,QAAQ,GAAG;IAClB;;aArBQE,SAAS,IAAIM;aACrBJ,YAAY;aACZJ,WAAW;;AAoBb"}