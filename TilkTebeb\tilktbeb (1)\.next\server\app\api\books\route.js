/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/books/route";
exports.ids = ["app/api/books/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbooks%2Froute&page=%2Fapi%2Fbooks%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbooks%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5CDesktop%5CProjects%5CAstewai%5CTilkTebeb%5Ctilktbeb%20(1)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5CDesktop%5CProjects%5CAstewai%5CTilkTebeb%5Ctilktbeb%20(1)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbooks%2Froute&page=%2Fapi%2Fbooks%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbooks%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5CDesktop%5CProjects%5CAstewai%5CTilkTebeb%5Ctilktbeb%20(1)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5CDesktop%5CProjects%5CAstewai%5CTilkTebeb%5Ctilktbeb%20(1)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_pc_Desktop_Projects_Astewai_TilkTebeb_tilktbeb_1_app_api_books_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/books/route.ts */ \"(rsc)/./app/api/books/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/books/route\",\n        pathname: \"/api/books\",\n        filename: \"route\",\n        bundlePath: \"app/api/books/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\api\\\\books\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_pc_Desktop_Projects_Astewai_TilkTebeb_tilktbeb_1_app_api_books_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbooks%2Froute&page=%2Fapi%2Fbooks%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbooks%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5CDesktop%5CProjects%5CAstewai%5CTilkTebeb%5Ctilktbeb%20(1)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5CDesktop%5CProjects%5CAstewai%5CTilkTebeb%5Ctilktbeb%20(1)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/api/books/route.ts":
/*!********************************!*\
  !*** ./app/api/books/route.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Sample database of books\nconst booksDB = [\n    {\n        id: \"the-psychology-of-money\",\n        title: \"The Psychology Of Money\",\n        author: \"Morgan Housel\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Finance\",\n        rating: 4.4,\n        pages: 242,\n        language: \"English\",\n        summary: `\n      <p>The Psychology of Money explores how money moves around in an economy and how people behave with it. The author, Morgan Housel, provides timeless lessons on wealth, greed, and happiness.</p>\n      \n      <h3>Key Insights:</h3>\n      \n      <p><strong>1. Financial success is not a hard science</strong></p>\n      <p>It's a soft skill where how you behave is more important than what you know. Financial outcomes are driven by luck, independent of intelligence and effort.</p>\n      \n      <p><strong>2. No one is crazy with money</strong></p>\n      <p>People make financial decisions based on their unique experiences, their own personal history, unique worldview, ego, pride, marketing, and odd incentives.</p>\n      \n      <p><strong>3. Luck and risk are siblings</strong></p>\n      <p>They are both the reality that every outcome in life is guided by forces other than individual effort. They both happen because the world is too complex to allow 100% of your actions to dictate 100% of your outcomes.</p>\n      \n      <p><strong>4. Never enough</strong></p>\n      <p>When rich people do crazy things, it's often a case of trying to feel valued after already having more money than they know what to do with.</p>\n    `,\n        keyInsights: `\n      <p><strong>1. Financial success is not a hard science</strong></p>\n      <p>It's a soft skill where how you behave is more important than what you know. Financial outcomes are driven by luck, independent of intelligence and effort.</p>\n      \n      <p><strong>2. No one is crazy with money</strong></p>\n      <p>People make financial decisions based on their unique experiences, their own personal history, unique worldview, ego, pride, marketing, and odd incentives.</p>\n      \n      <p><strong>3. Luck and risk are siblings</strong></p>\n      <p>They are both the reality that every outcome in life is guided by forces other than individual effort. They both happen because the world is too complex to allow 100% of your actions to dictate 100% of your outcomes.</p>\n      \n      <p><strong>4. Never enough</strong></p>\n      <p>When rich people do crazy things, it's often a case of trying to feel valued after already having more money than they know what to do with.</p>\n    `,\n        applications: `\n      <p><strong>For Personal Finance:</strong></p>\n      <ul>\n        <li>Save money without a specific goal in mind</li>\n        <li>Gain control over your time</li>\n        <li>Be reasonable rather than rational</li>\n        <li>Aim for enough, not for maximum</li>\n      </ul>\n      \n      <p><strong>For Investors:</strong></p>\n      <ul>\n        <li>Understand the role of luck and risk</li>\n        <li>Know that getting wealthy and staying wealthy are different skills</li>\n        <li>Long tails drive everything - a small number of events can account for the majority of outcomes</li>\n        <li>Use room for error when investing - prepare for a range of outcomes</li>\n      </ul>\n    `,\n        isPremium: false\n    },\n    {\n        id: \"atomic-habits\",\n        title: \"Atomic Habits\",\n        author: \"James Clear\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Productivity\",\n        rating: 4.9,\n        pages: 320,\n        language: \"English\",\n        summary: `\n      <p>Atomic Habits offers a proven framework for improving every day. James Clear reveals practical strategies that will teach you exactly how to form good habits, break bad ones, and master the tiny behaviors that lead to remarkable results.</p>\n      \n      <h3>Key Insights:</h3>\n      \n      <p><strong>1. Habits are the compound interest of self-improvement</strong></p>\n      <p>Small changes often appear to make no difference until you cross a critical threshold. The effects of your habits multiply as you repeat them.</p>\n      \n      <p><strong>2. Focus on systems instead of goals</strong></p>\n      <p>Goals are about the results you want to achieve. Systems are about the processes that lead to those results. Focus on the system, not the goal.</p>\n      \n      <p><strong>3. The Four Laws of Behavior Change</strong></p>\n      <p>Make it obvious, make it attractive, make it easy, and make it satisfying. These are the fundamental principles behind habit formation.</p>\n      \n      <p><strong>4. Identity-based habits</strong></p>\n      <p>The most effective way to change your habits is to focus not on what you want to achieve, but on who you wish to become.</p>\n    `,\n        keyInsights: `\n      <p><strong>1. Habits are the compound interest of self-improvement</strong></p>\n      <p>Small changes often appear to make no difference until you cross a critical threshold. The effects of your habits multiply as you repeat them.</p>\n      \n      <p><strong>2. Focus on systems instead of goals</strong></p>\n      <p>Goals are about the results you want to achieve. Systems are about the processes that lead to those results. Focus on the system, not the goal.</p>\n      \n      <p><strong>3. The Four Laws of Behavior Change</strong></p>\n      <p>Make it obvious, make it attractive, make it easy, and make it satisfying. These are the fundamental principles behind habit formation.</p>\n      \n      <p><strong>4. Identity-based habits</strong></p>\n      <p>The most effective way to change your habits is to focus not on what you want to achieve, but on who you wish to become.</p>\n    `,\n        applications: `\n      <p><strong>For Personal Development:</strong></p>\n      <ul>\n        <li>Start with an incredibly small habit</li>\n        <li>Increase your habit in very small ways</li>\n        <li>Break habits into chunks</li>\n        <li>When you slip, get back on track quickly</li>\n      </ul>\n      \n      <p><strong>For Business:</strong></p>\n      <ul>\n        <li>Create an environment where doing the right thing is as easy as possible</li>\n        <li>Make good habits obvious in your environment</li>\n        <li>Reduce friction for good habits</li>\n        <li>Increase friction for bad habits</li>\n      </ul>\n    `,\n        isPremium: false\n    },\n    {\n        id: \"sapiens\",\n        title: \"Sapiens\",\n        author: \"Yuval Noah Harari\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"History\",\n        rating: 4.7,\n        pages: 464,\n        language: \"English\",\n        summary: `\n      <p>Sapiens: A Brief History of Humankind is a book by Yuval Noah Harari that explores the history and impact of Homo sapiens on the world. It traces the evolution of our species from the emergence of Homo sapiens in Africa to our current status as the dominant force on Earth.</p>\n      \n      <h3>Key Insights:</h3>\n      \n      <p><strong>1. The Cognitive Revolution</strong></p>\n      <p>Around 70,000 years ago, Homo sapiens developed unique cognitive abilities, particularly the capacity for fiction and imagination, which allowed for unprecedented cooperation among large groups.</p>\n      \n      <p><strong>2. The Agricultural Revolution</strong></p>\n      <p>Beginning about 12,000 years ago, humans transitioned from hunting and gathering to farming, which Harari describes as \"history's biggest fraud\" because it led to harder work, less leisure, and poorer health for most people.</p>\n      \n      <p><strong>3. The Unification of Humankind</strong></p>\n      <p>Over time, humans have created increasingly larger networks of cooperation through shared myths, including money, empires, and religions.</p>\n      \n      <p><strong>4. The Scientific Revolution</strong></p>\n      <p>The last 500 years have seen an explosion of human power through the willingness to admit ignorance and the development of the scientific method.</p>\n    `,\n        keyInsights: `\n      <p><strong>1. The Cognitive Revolution</strong></p>\n      <p>Around 70,000 years ago, Homo sapiens developed unique cognitive abilities, particularly the capacity for fiction and imagination, which allowed for unprecedented cooperation among large groups.</p>\n      \n      <p><strong>2. The Agricultural Revolution</strong></p>\n      <p>Beginning about 12,000 years ago, humans transitioned from hunting and gathering to farming, which Harari describes as \"history's biggest fraud\" because it led to harder work, less leisure, and poorer health for most people.</p>\n      \n      <p><strong>3. The Unification of Humankind</strong></p>\n      <p>Over time, humans have created increasingly larger networks of cooperation through shared myths, including money, empires, and religions.</p>\n      \n      <p><strong>4. The Scientific Revolution</strong></p>\n      <p>The last 500 years have seen an explosion of human power through the willingness to admit ignorance and the development of the scientific method.</p>\n    `,\n        applications: `\n      <p><strong>For Understanding Society:</strong></p>\n      <ul>\n        <li>Recognize how shared myths and stories shape our world</li>\n        <li>Understand the historical context of current social structures</li>\n        <li>Question whether \"progress\" always means improvement</li>\n        <li>Consider the ethical implications of technological advancement</li>\n      </ul>\n      \n      <p><strong>For Business and Leadership:</strong></p>\n      <ul>\n        <li>Appreciate how shared narratives create cohesion in organizations</li>\n        <li>Understand how money and corporations are social constructs that depend on trust</li>\n        <li>Consider the long-term implications of short-term decisions</li>\n        <li>Recognize patterns of human behavior that persist across time</li>\n      </ul>\n    `,\n        isPremium: true\n    },\n    {\n        id: \"zero-to-one\",\n        title: \"Zero to One\",\n        author: \"Peter Thiel\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Entrepreneurship\",\n        rating: 4.8,\n        pages: 224,\n        language: \"English\",\n        summary: `\n      <p>Zero to One presents at once an optimistic view of the future of progress in America and a new way of thinking about innovation: it starts by learning to ask the questions that lead you to find value in unexpected places.</p>\n      \n      <h3>Key Insights:</h3>\n      \n      <p><strong>1. Vertical vs. Horizontal Progress</strong></p>\n      <p>Horizontal progress means copying things that work—going from 1 to n. Vertical progress means doing new things—going from 0 to 1. The focus should be on creating something new rather than copying existing models.</p>\n      \n      <p><strong>2. Monopolies vs. Competition</strong></p>\n      <p>Competition is for losers. Under perfect competition, no company makes economic profit. The goal should be to create a monopoly through innovation.</p>\n      \n      <p><strong>3. The Power Law</strong></p>\n      <p>A small number of companies radically outperform all others. This principle applies to venture capital investments, where a single investment may return more than all others combined.</p>\n      \n      <p><strong>4. Secrets</strong></p>\n      <p>Every great business is built around a secret that's hidden from the outside. Great companies find value in unexpected places by thinking about business from first principles.</p>\n    `,\n        keyInsights: `\n      <p><strong>1. Vertical vs. Horizontal Progress</strong></p>\n      <p>Horizontal progress means copying things that work—going from 1 to n. Vertical progress means doing new things—going from 0 to 1. The focus should be on creating something new rather than copying existing models.</p>\n      \n      <p><strong>2. Monopolies vs. Competition</strong></p>\n      <p>Competition is for losers. Under perfect competition, no company makes economic profit. The goal should be to create a monopoly through innovation.</p>\n      \n      <p><strong>3. The Power Law</strong></p>\n      <p>A small number of companies radically outperform all others. This principle applies to venture capital investments, where a single investment may return more than all others combined.</p>\n      \n      <p><strong>4. Secrets</strong></p>\n      <p>Every great business is built around a secret that's hidden from the outside. Great companies find value in unexpected places by thinking about business from first principles.</p>\n    `,\n        applications: `\n      <p><strong>For Entrepreneurs:</strong></p>\n      <ul>\n        <li>Focus on creating something new rather than improving existing products</li>\n        <li>Aim to create a monopoly through unique technology, network effects, economies of scale, and branding</li>\n        <li>Start small and monopolize a niche market before expanding</li>\n        <li>Build a great team with a strong, unified vision</li>\n      </ul>\n      \n      <p><strong>For Investors:</strong></p>\n      <ul>\n        <li>Understand that returns follow a power law—a few investments will outperform all others</li>\n        <li>Look for companies with proprietary technology, network effects, economies of scale, and strong branding</li>\n        <li>Evaluate the founding team's dynamics and vision</li>\n        <li>Consider whether the company has discovered a unique \"secret\" about the market</li>\n      </ul>\n    `,\n        isPremium: true\n    },\n    {\n        id: \"good-to-great\",\n        title: \"Good to Great\",\n        author: \"Jim Collins\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Leadership\",\n        rating: 4.7,\n        pages: 320,\n        language: \"English\",\n        summary: `\n      <p>Good to Great presents the findings of a five-year study by Jim Collins and his research team. The team identified a set of companies that made the leap from good results to great results and sustained those results for at least fifteen years.</p>\n      \n      <h3>Key Insights:</h3>\n      \n      <p><strong>1. Level 5 Leadership</strong></p>\n      <p>Leaders who have a unique blend of personal humility and professional will. They are ambitious for the company, not themselves.</p>\n      \n      <p><strong>2. First Who, Then What</strong></p>\n      <p>Get the right people on the bus, the wrong people off the bus, and the right people in the right seats—then figure out where to drive it.</p>\n      \n      <p><strong>3. Confront the Brutal Facts</strong></p>\n      <p>Create a culture where people have the opportunity to be heard and where the truth is heard. Maintain unwavering faith that you can and will prevail, regardless of difficulties.</p>\n      \n      <p><strong>4. The Hedgehog Concept</strong></p>\n      <p>Focus on the intersection of three circles: what you can be the best in the world at, what drives your economic engine, and what you are deeply passionate about.</p>\n    `,\n        keyInsights: `\n      <p><strong>1. Level 5 Leadership</strong></p>\n      <p>Leaders who have a unique blend of personal humility and professional will. They are ambitious for the company, not themselves.</p>\n      \n      <p><strong>2. First Who, Then What</strong></p>\n      <p>Get the right people on the bus, the wrong people off the bus, and the right people in the right seats—then figure out where to drive it.</p>\n      \n      <p><strong>3. Confront the Brutal Facts</strong></p>\n      <p>Create a culture where people have the opportunity to be heard and where the truth is heard. Maintain unwavering faith that you can and will prevail, regardless of difficulties.</p>\n      \n      <p><strong>4. The Hedgehog Concept</strong></p>\n      <p>Focus on the intersection of three circles: what you can be the best in the world at, what drives your economic engine, and what you are deeply passionate about.</p>\n    `,\n        applications: `\n      <p><strong>For Business Leaders:</strong></p>\n      <ul>\n        <li>Develop Level 5 Leadership qualities: ambition for the company over self</li>\n        <li>Focus on getting the right team in place before determining strategy</li>\n        <li>Create a culture of disciplined people, thought, and action</li>\n        <li>Apply the Hedgehog Concept to focus resources and efforts</li>\n      </ul>\n      \n      <p><strong>For Organizations:</strong></p>\n      <ul>\n        <li>Use technology as an accelerator, not a creator of momentum</li>\n        <li>Build momentum gradually until breakthrough occurs (the flywheel effect)</li>\n        <li>Maintain discipline to stick with what you can be best at</li>\n        <li>Confront reality while maintaining faith in ultimate success</li>\n      </ul>\n    `,\n        isPremium: true\n    },\n    {\n        id: \"the-lean-startup\",\n        title: \"The Lean Startup\",\n        author: \"Eric Ries\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Startup\",\n        rating: 4.6,\n        pages: 336,\n        language: \"English\",\n        summary: `\n      <p>The Lean Startup introduces a methodology for developing businesses and products that aims to shorten product development cycles and rapidly discover if a proposed business model is viable.</p>\n      \n      <h3>Key Insights:</h3>\n      \n      <p><strong>1. Build-Measure-Learn</strong></p>\n      <p>The fundamental activity of a startup is to turn ideas into products, measure how customers respond, and then learn whether to pivot or persevere.</p>\n      \n      <p><strong>2. Minimum Viable Product (MVP)</strong></p>\n      <p>The version of a new product that allows a team to collect the maximum amount of validated learning about customers with the least effort.</p>\n      \n      <p><strong>3. Validated Learning</strong></p>\n      <p>The process of demonstrating empirically that a team has discovered valuable truths about a startup's present and future business prospects.</p>\n      \n      <p><strong>4. Innovation Accounting</strong></p>\n      <p>A quantitative approach that allows startups to prove objectively that they are learning how to grow a sustainable business.</p>\n    `,\n        keyInsights: `\n      <p><strong>1. Build-Measure-Learn</strong></p>\n      <p>The fundamental activity of a startup is to turn ideas into products, measure how customers respond, and then learn whether to pivot or persevere.</p>\n      \n      <p><strong>2. Minimum Viable Product (MVP)</strong></p>\n      <p>The version of a new product that allows a team to collect the maximum amount of validated learning about customers with the least effort.</p>\n      \n      <p><strong>3. Validated Learning</strong></p>\n      <p>The process of demonstrating empirically that a team has discovered valuable truths about a startup's present and future business prospects.</p>\n      \n      <p><strong>4. Innovation Accounting</strong></p>\n      <p>A quantitative approach that allows startups to prove objectively that they are learning how to grow a sustainable business.</p>\n    `,\n        applications: `\n      <p><strong>For Entrepreneurs:</strong></p>\n      <ul>\n        <li>Start with a minimum viable product to test assumptions quickly</li>\n        <li>Use actionable metrics that demonstrate clear cause and effect</li>\n        <li>Practice continuous deployment and small batch sizes</li>\n        <li>Be willing to pivot when necessary based on validated learning</li>\n      </ul>\n      \n      <p><strong>For Established Companies:</strong></p>\n      <ul>\n        <li>Create innovation teams with appropriate structures and metrics</li>\n        <li>Allocate resources using innovation accounting</li>\n        <li>Develop internal entrepreneurship through dedicated teams</li>\n        <li>Apply lean principles to accelerate product development cycles</li>\n      </ul>\n    `,\n        isPremium: true\n    },\n    {\n        id: \"think-and-grow-rich\",\n        title: \"Think and Grow Rich\",\n        author: \"Napoleon Hill\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Wealth\",\n        rating: 4.7,\n        pages: 238,\n        language: \"English\",\n        summary: `\n      <p>Think and Grow Rich is a personal development and self-improvement book. While the title implies that this book deals only with how to achieve monetary wealth, the author explains that the philosophy taught in the book can be used to help people succeed in all lines of work and to do or be almost anything they want.</p>\n      \n      <h3>Key Insights:</h3>\n      \n      <p><strong>1. Desire</strong></p>\n      <p>The starting point of all achievement. Not a hope, not a wish, but a keen pulsating desire which transcends everything.</p>\n      \n      <p><strong>2. Faith</strong></p>\n      <p>Visualization of, and belief in, the attainment of desire. The emotion of faith, love, and sex are the most powerful of all the major positive emotions.</p>\n      \n      <p><strong>3. Autosuggestion</strong></p>\n      <p>The medium for influencing the subconscious mind. Self-suggestion is the agency of control through which an individual may voluntarily feed his subconscious mind on thoughts of a creative nature.</p>\n      \n      <p><strong>4. Specialized Knowledge</strong></p>\n      <p>Personal experiences or observations. Knowledge will not attract money, unless it is organized, and intelligently directed, through practical plans of action.</p>\n    `,\n        keyInsights: `\n      <p><strong>1. Desire</strong></p>\n      <p>The starting point of all achievement. Not a hope, not a wish, but a keen pulsating desire which transcends everything.</p>\n      \n      <p><strong>2. Faith</strong></p>\n      <p>Visualization of, and belief in, the attainment of desire. The emotion of faith, love, and sex are the most powerful of all the major positive emotions.</p>\n      \n      <p><strong>3. Autosuggestion</strong></p>\n      <p>The medium for influencing the subconscious mind. Self-suggestion is the agency of control through which an individual may voluntarily feed his subconscious mind on thoughts of a creative nature.</p>\n      \n      <p><strong>4. Specialized Knowledge</strong></p>\n      <p>Personal experiences or observations. Knowledge will not attract money, unless it is organized, and intelligently directed, through practical plans of action.</p>\n    `,\n        applications: `\n      <p><strong>For Personal Development:</strong></p>\n      <ul>\n        <li>Set clear, specific goals with deadlines</li>\n        <li>Develop a burning desire to achieve your goals</li>\n        <li>Create a definite plan and take immediate action</li>\n        <li>Use autosuggestion to influence your subconscious mind</li>\n      </ul>\n      \n      <p><strong>For Business Success:</strong></p>\n      <ul>\n        <li>Join or create a mastermind group for collective intelligence</li>\n        <li>Transform setbacks into stepping stones through persistence</li>\n        <li>Harness specialized knowledge through continuous learning</li>\n        <li>Make decisions quickly and change them slowly, if at all</li>\n      </ul>\n    `,\n        isPremium: true\n    },\n    {\n        id: \"the-7-habits-of-highly-effective-people\",\n        title: \"The 7 Habits of Highly Effective People\",\n        author: \"Stephen Covey\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Self-Development\",\n        rating: 4.8,\n        pages: 432,\n        language: \"English\",\n        summary: `\n      <p>The 7 Habits of Highly Effective People presents an approach to being effective in attaining goals by aligning oneself to what Covey calls \"true north\" principles based on a character ethic that he presents as universal and timeless.</p>\n      \n      <h3>Key Insights:</h3>\n      \n      <p><strong>1. Be Proactive</strong></p>\n      <p>Take responsibility for your life. Proactive people focus on what they can control rather than what they cannot.</p>\n      \n      <p><strong>2. Begin with the End in Mind</strong></p>\n      <p>Define clear measures of success and a plan to achieve them. Start with a clear destination to understand where you are now, where you're going, and what you value most.</p>\n      \n      <p><strong>3. Put First Things First</strong></p>\n      <p>Prioritize and execute your most important tasks based on importance rather than urgency. What matters most should never be at the mercy of what matters least.</p>\n      \n      <p><strong>4. Think Win-Win</strong></p>\n      <p>Seek mutual benefit in all human interactions. Win-win is a frame of mind that constantly seeks mutual benefit in all interactions.</p>\n    `,\n        keyInsights: `\n      <p><strong>1. Be Proactive</strong></p>\n      <p>Take responsibility for your life. Proactive people focus on what they can control rather than what they cannot.</p>\n      \n      <p><strong>2. Begin with the End in Mind</strong></p>\n      <p>Define clear measures of success and a plan to achieve them. Start with a clear destination to understand where you are now, where you're going, and what you value most.</p>\n      \n      <p><strong>3. Put First Things First</strong></p>\n      <p>Prioritize and execute your most important tasks based on importance rather than urgency. What matters most should never be at the mercy of what matters least.</p>\n      \n      <p><strong>4. Think Win-Win</strong></p>\n      <p>Seek mutual benefit in all human interactions. Win-win is a frame of mind that constantly seeks mutual benefit in all interactions.</p>\n    `,\n        applications: `\n      <p><strong>For Personal Effectiveness:</strong></p>\n      <ul>\n        <li>Focus on your Circle of Influence rather than your Circle of Concern</li>\n        <li>Create a personal mission statement to guide your decisions</li>\n        <li>Use time management matrix to prioritize important but not urgent tasks</li>\n        <li>Seek first to understand, then to be understood in communications</li>\n      </ul>\n      \n      <p><strong>For Leadership:</strong></p>\n      <ul>\n        <li>Build trust through character and competence</li>\n        <li>Create win-win performance agreements</li>\n        <li>Practice empathic listening to understand others' perspectives</li>\n        <li>Value differences and create synergy through creative cooperation</li>\n      </ul>\n    `,\n        isPremium: true\n    }\n];\n// GET handler for retrieving all books or filtered books\nasync function GET(request) {\n    const { searchParams } = new URL(request.url);\n    // Extract query parameters\n    const category = searchParams.get(\"category\");\n    const query = searchParams.get(\"query\");\n    const premium = searchParams.get(\"premium\");\n    const limit = Number.parseInt(searchParams.get(\"limit\") || \"100\");\n    // Filter books based on query parameters\n    let filteredBooks = [\n        ...booksDB\n    ];\n    if (category && category !== \"all\") {\n        filteredBooks = filteredBooks.filter((book)=>book.category.toLowerCase() === category.toLowerCase());\n    }\n    if (query) {\n        const searchQuery = query.toLowerCase();\n        filteredBooks = filteredBooks.filter((book)=>book.title.toLowerCase().includes(searchQuery) || book.author.toLowerCase().includes(searchQuery));\n    }\n    if (premium !== null) {\n        const isPremium = premium === \"true\";\n        filteredBooks = filteredBooks.filter((book)=>book.isPremium === isPremium);\n    }\n    // Limit the number of results\n    filteredBooks = filteredBooks.slice(0, limit);\n    // Convert to BookPreview type to reduce payload size\n    const bookPreviews = filteredBooks.map((book)=>({\n            id: book.id,\n            title: book.title,\n            author: book.author,\n            coverUrl: book.coverUrl,\n            category: book.category,\n            rating: book.rating\n        }));\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(bookPreviews);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/books/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbooks%2Froute&page=%2Fapi%2Fbooks%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbooks%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5CDesktop%5CProjects%5CAstewai%5CTilkTebeb%5Ctilktbeb%20(1)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5CDesktop%5CProjects%5CAstewai%5CTilkTebeb%5Ctilktbeb%20(1)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();